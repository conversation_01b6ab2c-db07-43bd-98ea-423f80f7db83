import * as dotenv from 'dotenv'
dotenv.config()
import { z } from 'zod'

const APP = z
	.object({
		port: z.number(),
		nodeEnv: z.string(),
		origin: z.string().transform(val => val.split(',').map(v => v.trim())),
	})
	.parse({
		port: Number(process.env.APP_PORT),
		nodeEnv: process.env.NODE_ENV,
		origin: process.env.ORIGIN,
	})

const JWT = z
	.object({
		secret: z.string(),
	})
	.parse({
		secret: process.env.JWT_SECRET,
	})

const HASH = z
	.object({
		secret: z.string(),
	})
	.parse({
		secret: process.env.HASH_SECRET,
	})

const ADMIN = z
	.object({
		alternateEmail: z.string(),
	})
	.parse({
		alternateEmail: process.env.ADMIN_ALTERNATE_EMAIL,
	})

const DB = z
	.object({
		hostUrl: z.string(),
		name: z.string(),
	})
	.parse({
		hostUrl: process.env.DB_HOST_URL,
		name: process.env.DB_NAME,
	})

const MAIL = z
	.object({
		from: z.string().email(),
		domain: z.string(),
		mailgunApiKey: z.string(),
		brevoApiKey: z.string(),
	})
	.parse({
		from: process.env.MAILGUN_FROM,
		domain: process.env.MAILGUN_DOMAIN,
		mailgunApiKey: process.env.MAILGUN_API_KEY,
		brevoApiKey: process.env.BREVO_API_KEY,
	})

const SMS = z
	.object({
		authKey: z.string(),
		sender: z.string(),
		route: z.string(),
		country: z.string(),
	})
	.parse({
		authKey: process.env.SMS_AUTH_KEY,
		sender: process.env.SMS_SENDER,
		route: process.env.SMS_ROUTE,
		country: process.env.SMS_COUNTRY,
	})

const SWAGGER_STATS = z
	.object({
		username: z.string(),
		password: z.string(),
	})
	.parse({
		username: process.env.SWAGGER_STATS_USERNAME,
		password: process.env.SWAGGER_STATS_PASSWORD,
	})

const CLOUDINARY = z
	.object({
		cloudName: z.string(),
		apiKey: z.string(),
		apiSecret: z.string(),
	})
	.parse({
		cloudName: process.env.CLOUDINARY_NAME,
		apiKey: process.env.CLOUDINARY_API_KEY,
		apiSecret: process.env.CLOUDINARY_API_SECRET,
	})

const REDIS = z
	.object({
		url: z.string(),
	})
	.parse({
		url: process.env.REDIS_URL,
	})

const SESSION = z
	.object({
		secret: z.string(),
	})
	.parse({
		secret: process.env.SESSION_SECRET,
	})

const RAZORPAY = z
	.object({
		keyId: z.string(),
		keySecret: z.string(),
	})
	.parse({
		keyId: process.env.RAZORPAY_KEY_ID,
		keySecret: process.env.RAZORPAY_KEY_SECRET,
	})

const MEILISEARCH = z
	.object({
		name: z.string(),
		url: z.string(),
		key: z.string(),
	})
	.parse({
		name: process.env.MEILISEARCH_NAME,
		url: process.env.MEILISEARCH_URL,
		key: process.env.MEILISEARCH_KEY,
	})

const AWS_BUCKET = z
	.object({
		accessKeyId: z.string(),
		secretAccessKey: z.string(),
		bucketName: z.string(),
		region: z.string(),
	})
	.parse({
		accessKeyId: process.env.AWS_ACCESS_KEY_ID,
		secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
		bucketName: process.env.AWS_BUCKET_NAME,
		region: process.env.AWS_REGION,
	})

const MIGRATION = z
	.object({
		sourceUrl: z.string(),
		targetUrl: z.string(),
		sourceDatabase: z.string(),
		targetDatabase: z.string(),
	})
	.parse({
		sourceUrl: process.env.SOURCE_URL,
		targetUrl: process.env.TARGET_URL,
		sourceDatabase: process.env.SOURCE_DATABASE,
		targetDatabase: process.env.TARGET_DATABASE,
	})

const GOOGLE_OAUTH = z
	.object({
		clientId: z.string(),
		clientSecret: z.string(),
		callbackUrl: z.string(),
		authUrl: z.string().default('https://accounts.google.com/o/oauth2/v2/auth'),
	})
	.parse({
		clientId: process.env.GOOGLE_CLIENT_ID,
		clientSecret: process.env.GOOGLE_CLIENT_SECRET,
		callbackUrl: process.env.GOOGLE_CALLBACK_URL,
		authUrl:
			process.env.GOOGLE_AUTH_URL ||
			'https://accounts.google.com/o/oauth2/v2/auth',
	})

const TELEGRAM = z
	.object({
		botToken: z.string(),
	})
	.parse({
		botToken: process.env.TELEGRAM_BOT_TOKEN,
	})

const LISTMONK = z
	.object({
		baseUrl: z.string(),
		username: z.string(),
		password: z.string(),
	})
	.parse({
		baseUrl: process.env.LISTMONK_BASE_URL,
		username: process.env.LISTMONK_USERNAME,
		password: process.env.LISTMONK_PASSWORD,
	})

export const env = {
	APP,
	JWT,
	ADMIN,
	SMS,
	DB,
	MEILISEARCH,
	MAIL,
	SESSION,
	SWAGGER_STATS,
	REDIS,
	CLOUDINARY,
	HASH,
	RAZORPAY,
	AWS_BUCKET,
	MIGRATION,
	GOOGLE_OAUTH,
	TELEGRAM,
	LISTMONK,
}
