import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common'
import {
	ApiBearerAuth,
	ApiCookieAuth,
	ApiUnauthorizedResponse,
} from '@nestjs/swagger'
import { AuthenticatedGuard } from 'app/auth/authenticated.guard'
import { OptionalAuthGuard } from 'app/auth/optional-auth.guard'
import { Csrf } from 'ncsrf'

export function Auth() {
	return applyDecorators(
		//FIXME - Csrf(),
		// Csrf(),
		UseGuards(AuthenticatedGuard),
		ApiCookieAuth(),
		ApiUnauthorizedResponse({ description: 'Unauthorized' }),
	)
}

export function AuthOptional() {
	return applyDecorators(
		SetMetadata('isOptional', true),
		UseGuards(OptionalAuthGuard),
		ApiCookieAuth(),
		ApiUnauthorizedResponse({ description: 'Unauthorized' }),
	)
}
