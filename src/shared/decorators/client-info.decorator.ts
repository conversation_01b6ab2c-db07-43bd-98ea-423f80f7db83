// client-info.decorator.ts
import { ExecutionContext, createParamDecorator } from '@nestjs/common'
import * as geoip from 'geoip-lite'
import useragent from 'useragent'

export interface ClientInfoData {
	ip: string
	geo: geoip.Lookup | null
	userAgent: string
	device: string
	os: string
	browser: string
}

export const ClientInfo = createParamDecorator(
	(data: unknown, ctx: ExecutionContext): ClientInfoData => {
		const request = ctx.switchToHttp().getRequest()
		const ip =
			request.headers['x-forwarded-for'] || request.connection.remoteAddress
		const geo = geoip.lookup(ip)
		const agent = useragent.lookup(request.headers['user-agent'])

		return {
			ip,
			geo,
			userAgent: request.headers['user-agent'],
			device: agent.device.toString(),
			os: agent.os.toString(),
			browser: agent.toAgent(),
		}
	},
)
