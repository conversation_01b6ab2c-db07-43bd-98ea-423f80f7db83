/**
 * Validates if a URL is from a supported store
 * @param url URL to validate
 * @returns Object with validation results
 */
export function validateStoreUrl(url: string): {
	isValid: boolean
	isAmazon: boolean
	isFlipkart: boolean
	message?: string
} {
	if (!url) {
		return {
			isValid: false,
			isAmazon: false,
			isFlipkart: false,
			message: 'URL is required',
		}
	}

	try {
		// Basic URL validation
		new URL(url)

		// Check if URL is from supported stores
		const isAmazon = url.includes('amazon.in') || url.includes('amazon.com')
		const isFlipkart = url.includes('flipkart.com')

		if (!isAmazon && !isFlipkart) {
			return {
				isValid: false,
				isAmazon: false,
				isFlipkart: false,
				message: 'URL must be from Amazon or Flipkart',
			}
		}

		return {
			isValid: true,
			isAmazon,
			isFlipkart,
		}
	} catch (error) {
		return {
			isValid: false,
			isAmazon: false,
			isFlipkart: false,
			message: 'Invalid URL format',
		}
	}
}

/**
 * Generates an Amazon affiliate URL
 * @param url Original Amazon URL
 * @returns Affiliate URL with tag parameter
 */
export function generateAmazonAffiliateUrl(url: string): string {
	const tag = 'revo21-21'

	if (url.indexOf('?') !== -1) {
		return `${url}&tag=${tag}`
	}
	return `${url}?tag=${tag}`
}

/**
 * Generates a Flipkart affiliate URL
 * @param url Original Flipkart URL
 * @returns Affiliate URL with affid parameter
 */
export function generateFlipkartAffiliateUrl(url: string): string {
	const affid = 'connectin'
	const sanitizedLink = url.replace('https://www.flipkart.com/', '')

	if (url.indexOf('?') !== -1) {
		return `https://dl.flipkart.com/dl/${sanitizedLink}&affid=${affid}`
	}
	return `https://dl.flipkart.com/dl/${sanitizedLink}?affid=${affid}`
}

/**
 * Generates an affiliate URL based on the store
 * @param url Original URL
 * @param isAmazon Whether the URL is from Amazon
 * @param isFlipkart Whether the URL is from Flipkart
 * @returns Generated affiliate URL
 */
export function generateAffiliateUrl(
	url: string,
	isAmazon: boolean,
	isFlipkart: boolean,
): string {
	if (isAmazon) {
		return generateAmazonAffiliateUrl(url)
	}
	if (isFlipkart) {
		return generateFlipkartAffiliateUrl(url)
	}

	return url
}
