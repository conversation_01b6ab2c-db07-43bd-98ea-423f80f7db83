import { affiliateIds } from 'shared/constants/affiliate.constants'

/**
 * Validates if a URL is from a supported store
 * @param url URL to validate
 * @returns Object with validation results
 */
export function validateStoreUrl(url: string): {
	isValid: boolean
	isAmazon: boolean
	isFlipkart: boolean
	message?: string
} {
	if (!url) {
		return {
			isValid: false,
			isAmazon: false,
			isFlipkart: false,
			message: 'URL is required',
		}
	}

	try {
		// Basic URL validation
		new URL(url)

		// Check if URL is from supported stores
		const isAmazon = url.includes('amazon.in') || url.includes('amzn.in')
		const isFlipkart = url.includes('flipkart.com')

		if (!isAmazon && !isFlipkart) {
			return {
				isValid: false,
				isAmazon: false,
				isFlipkart: false,
				message: 'URL must be from Amazon or Flipkart',
			}
		}

		return {
			isValid: true,
			isAmazon,
			isFlipkart,
		}
	} catch (error) {
		return {
			isValid: false,
			isAmazon: false,
			isFlipkart: false,
			message: 'Invalid URL format',
		}
	}
}

/**
 * Generates an affiliate URL based on the affiliation type and original URL
 * @param url Original URL
 * @param affiliationType The type of affiliation
 * @param baseLink Optional base link for some affiliation types
 * @returns Generated affiliate URL
 */
export function generateAffiliateUrlByType(
	url: string,
	affiliationType: string,
	baseLink?: string,
): string {
	if (!url) return ''

	switch (affiliationType) {
		case affiliateIds.admitad: {
			const encodedOfferLink = encodeURIComponent(url)
			return `${baseLink}?ulp=${encodedOfferLink}`
		}

		case affiliateIds.amazon: {
			if (url.indexOf('?') !== -1) {
				return `${url}&tag=revo21-21`
			}
			return `${url}?tag=revo21-21`
		}

		case affiliateIds.souq: {
			const affiliateParams =
				'&phgid=1101lKgZ&pubref=||||&utm_source=affiliate_hub&utm_medium=cpt&utm_content=affiliate&utm_campaign=100l2&u_type=text&u_title=&u_c=&u_fmt=&u_a=1101l11808&u_as=||||'

			if (url.indexOf('?') !== -1) {
				return `${url}${affiliateParams}`
			}
			return `${url}?${affiliateParams.substring(1)}` // Remove the leading "&" for the first parameter
		}

		case affiliateIds.flipkart: {
			const sanitizedLink = url.replace('https://www.flipkart.com/', '')

			if (url.indexOf('?') !== -1) {
				return `https://dl.flipkart.com/dl/${sanitizedLink}&affid=connectin`
			}
			return `https://dl.flipkart.com/dl/${sanitizedLink}?affid=connectin`
		}

		case affiliateIds.adsplay: {
			if (!baseLink) return url

			const processedBaseLink = baseLink.replace(
				'http://affiliates.adsplay.in/trackingcode.php?',
				'',
			)
			return `http://affiliates.adsplay.in/trackingcode_deep.php?${processedBaseLink}&dlurl=${url}`
		}

		case affiliateIds.shopclues: {
			if (!baseLink) return url

			const sanitizedOfferLink = encodeURIComponent(url)
			const updatedBaseLink = baseLink.split('&ckmrdr=')[0]
			return `${updatedBaseLink}&ckmrdr=${sanitizedOfferLink}`
		}

		case affiliateIds.infibeam: {
			if (url.indexOf('?') !== -1) {
				return `${url}&trackId=icash`
			}
			return `${url}?trackId=icash`
		}

		case affiliateIds.twogud: {
			if (url.indexOf('?') !== -1) {
				return `${url}&affid=connectin`
			}
			return `${url}?affid=connectin`
		}

		case affiliateIds.omg: {
			if (!baseLink) return url

			const sanitizedBaseLink = baseLink.split('&r=')[0]
			return `${sanitizedBaseLink}&r=${url}`
		}

		case affiliateIds.affle:
		case affiliateIds.flickstree:
		case affiliateIds.clickonik: {
			if (!baseLink) return url

			const encodedOfferLink = encodeURIComponent(url)
			const updatedBaseLink = baseLink.split('&url=')[0]
			return `${updatedBaseLink}&url=${encodedOfferLink}`
		}

		case affiliateIds.impact: {
			if (!baseLink) return url

			const encodedOfferLink = encodeURIComponent(url)
			const updatedBaseLink = baseLink.split('?u=')[0]
			return `${updatedBaseLink}?u=${encodedOfferLink}`
		}

		default:
			return url
	}
}
