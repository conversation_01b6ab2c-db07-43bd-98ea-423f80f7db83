import { SavedEnum, SavedItemType } from 'shared/types'

export function getSaveItemKeyByValue(
	value: string,
): SavedItemType | undefined {
	// Iterate over the keys of the enum
	const keys = Object.keys(SavedEnum) as SavedItemType[]

	// Iterate over the keys to find the matching key for the given value
	for (const key of keys) {
		if (SavedEnum[key] === value) {
			return key // Return the key if the value matches
		}
	}

	return undefined // Return undefined if no matching key is found
}
