//NOTE - currently adding IST offset to comparing it with UTC 0 time
// Get current IST as a Date object
//

// export const currentTime = new Date(new Date().getTime() + 330 * 60 * 1000)

// export const currentTime = new Date(new Date().getTime() + 5.5 * 60 * 60 * 1000);

const currentTimeInIst = new Date().toLocaleString('en-US', {
	timeZone: 'Asia/Kolkata',
})
export const currentTime = new Date(currentTimeInIst)

export const currentTimeTest = new Date()

import moment from 'moment-timezone'

/**
 * Converts the current time from a given time zone to UTC.
 *
 * @param timezone - The local time zone (e.g., 'Asia/Kolkata').
 * @returns A string representing the current UTC time in ISO 8601 format.
 */
export const convertLocalToUTC = (timezone = 'Asia/Kolkata'): string => {
	// Get the current time in the provided time zone
	const localTime = moment.tz(timezone)

	// Convert the local time to UTC and return it as ISO string
	return localTime.utc().toISOString()
}

export const convertLocalToUTCDate = (timezone = 'Asia/Kolkata'): Date => {
	// Get the current time in the provided time zone
	const localTime = moment.tz(timezone)

	// Convert the local time to UTC and return it as a Date object
	return localTime.utc().toDate()
}

/**
 * Converts a given UTC time to local time in a specified time zone.
 *
 * @param utcTime - The UTC time to be converted.
 * @param timezone - The local time zone (e.g., 'Asia/Kolkata').
 * @returns A string representing the local time in ISO 8601 format.
 */
export const convertUTCToLocal = (
	utcTime: string,
	timezone = 'Asia/Kolkata',
): string => {
	// Parse the UTC time and convert it to the specified time zone
	const localTime = moment.utc(utcTime).tz(timezone)

	// Return the local time as ISO string
	return localTime.toISOString()
}

/**
 * Returns the date after 7 days and 15 minutes if the given date is greater than 7 days from the current date.
 * Otherwise, returns the given date.
 *
 * @param date - The date to be checked.
 * @param timezone - The local time zone (e.g., 'Asia/Kolkata').
 * @returns A string representing the final date in UTC.
 */
export const getFinalDateToShow = (
	date: string,
	timezone = 'Asia/Kolkata',
): string => {
	const localExpiryDate = moment.utc(date).tz(timezone)
	const currentLocalDate = moment.tz(timezone)
	const sevenDaysFromNow = currentLocalDate
		.clone()
		.add(7, 'days')
		.add(15, 'minutes')

	if (localExpiryDate.isBefore(sevenDaysFromNow)) {
		return date // Return the original date if it's less than 7 days from now
	}

	return sevenDaysFromNow.utc().toISOString()
}
