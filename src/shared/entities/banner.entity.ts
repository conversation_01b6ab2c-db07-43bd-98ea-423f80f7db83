import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document, HydratedDocument, Types } from 'mongoose'
import { BannerTypes } from 'shared/enums'
import { Admin } from './admin.entity'
import { Image, ImageSchema } from './image.entity'

export type BannerDocument = Document & Banner

@Schema({ timestamps: true })
export class Banner {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({
		type: ImageSchema,
	})
	desktopBanner!: Image

	@Prop({
		type: ImageSchema,
	})
	mobileBanner!: Image

	@Prop({ type: String })
	redirectUrl!: string

	@Prop({ type: Date, required: true })
	expiryDate!: Date

	@Prop({
		type: String,
		enum: Object.values(BannerTypes),
	})
	type: string

	@Prop({ type: Number, default: 0 })
	priority: number

	@Prop({ type: String, required: true, default: 'en' })
	termsContent: string

	@Prop({ type: String, required: true, default: 'en' })
	termsTitle: string

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' }) // Assuming 'Admin' is the name of the model for admins
	updatedBy!: Admin

	@Prop({
		type: Boolean,
		default: true,
	})
	active!: boolean
}

export const BannerSchema = SchemaFactory.createForClass(Banner)
BannerSchema.set('toJSON', {
	transform: (doc, ret) => {
		ret.id = ret._id // Rename _id to id
		ret._id = undefined
		ret.__v = undefined
		ret.createdBy = undefined
	},
})
