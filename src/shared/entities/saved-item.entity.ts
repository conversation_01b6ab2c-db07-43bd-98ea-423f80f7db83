import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { SavedEnum } from 'shared/types'
import { User } from './user.entity'

export type SavedItemDocument = HydratedDocument<SavedItem>

@Schema({ timestamps: true })
export class SavedItem {
	@Prop({ type: Number, required: true, default: 0, unique: true, index: true })
	uid: number

	@Prop({
		type: String,
		required: true,
		enum: Object.values(SavedEnum),
	})
	type!: string

	@Prop({
		type: Number, // Assuming uid is of type string
		required: true,
		refPath: 'refModel', // Dynamic reference based on the refModel field
		field: 'uid',
	})
	itemUid!: number

	@Prop({
		type: String, // Specify the reference model dynamically
		required: true,
		enum: ['Offer', 'Store', 'GiftCard'], // Enumerate possible reference models
	})
	refModel!: string

	@Prop({ type: Types.ObjectId, required: true, ref: 'User', index: true })
	user!: User

	@Prop({
		required: true,
		type: Types.ObjectId,
		refPath: 'refModel',
		index: true,
	})
	item: Types.ObjectId
}

export const SavedItemSchema = SchemaFactory.createForClass(SavedItem)

//get offer data with uid ref
SavedItemSchema.set('toObject', { virtuals: true })
SavedItemSchema.set('toJSON', { virtuals: true })

// Define a virtual field to reference documents based on uid and refModel
// SavedItemSchema.virtual('associatedItem', {
//     ref: (doc: SavedItem) => doc.refModel, // Reference the model based on the refModel field
//     localField: 'itemUid', // The local field to use for the association
//     foreignField: 'uid', // The field in the referenced model to match against
// });
//

SavedItemSchema.pre('save', async function (next) {
	try {
		if (!this.isNew) {
			return next()
		}
		// Generate a unique value for the "uid" field
		const savedItemModel = this.constructor as Model<
			Document & SavedItemDocument
		>
		const lastDocument = await savedItemModel.findOne().sort({ _id: -1 })

		if (lastDocument) {
			this.uid = lastDocument.uid + 1
		} else {
			this.uid = 1
		}

		next()
	} catch (error) {
		next(error) // Pass any errors to the next middleware
	}
})
