import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { StoreCategory } from './category'
import { Categories } from './category/category-collection.entity'
import { Image, ImageSchema } from './image.entity'
import { StoreDocument } from './store.entity'

@Schema({ timestamps: true })
export class Offer {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: Types.ObjectId, required: true, index: true, ref: 'Store' })
	store!: StoreDocument

	@Prop({ type: Types.ObjectId, ref: 'StoreCategory' })
	storeCategory?: StoreCategory

	@Prop({
		type: Types.ArraySubdocument<Categories>,
		required: true,
		ref: 'Categories',
	})
	categories!: Categories[]

	@Prop({ type: String, required: true })
	title!: string

	@Prop({ type: String, enum: ['new', 'existing', 'both'] })
	userType: string

	@Prop({ type: String })
	offer?: string

	@Prop({ type: Number })
	discount?: number

	@Prop({ type: Number })
	itemPrice?: number

	@Prop({ type: String })
	url?: string

	@Prop({
		type: ImageSchema,
	})
	productImage!: Image

	@Prop({ type: String, required: true })
	description!: string

	@Prop({ type: Types.ObjectId, required: true, ref: 'Affiliation' })
	affiliation!: Affiliation

	@Prop({ type: String })
	couponCode?: string

	@Prop({ type: Boolean, default: false })
	isCoupon?: boolean

	@Prop({ type: String })
	link?: string

	@Prop({ type: Number })
	offerPercent!: number

	@Prop({ type: Number })
	offerAmount!: number

	@Prop({ type: String })
	howToGet?: string

	@Prop({ type: String })
	terms?: `<ul>${string}</ul>`

	@Prop({ type: Number, default: 0, index: true })
	priority?: number

	@Prop({ type: Number, default: 0, index: true })
	trendingPriority?: number

	@Prop({ type: String })
	repeatBy?: string

	@Prop({ type: Boolean, default: false })
	stockEnds?: boolean

	@Prop({ type: Date, index: true })
	dateExpiry?: Date

	@Prop({ type: Date })
	dateStart?: Date

	@Prop({ type: String })
	importantUpdate?: string

	@Prop({ type: String })
	keySpecs?: string

	@Prop({ type: Boolean, default: false })
	visibility?: boolean

	@Prop({ type: Boolean, default: true })
	active?: boolean

	@Prop({ type: Boolean, default: false })
	trending?: boolean

	@Prop({ type: String, enum: ['flat', 'upto'], default: 'upto' })
	offerType: 'flat' | 'upto'

	@Prop({ type: Boolean, default: false })
	missedDeal?: boolean

	@Prop({ type: Boolean, default: false })
	migrated?: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Boolean, default: false })
	isAutoGenerated?: boolean

	@Prop({ type: Boolean, default: false })
	hideCbTag?: boolean
}
export type OfferDocument = HydratedDocument<Offer>

const OfferSchema = SchemaFactory.createForClass(Offer)

// Disable automatic validation before save
OfferSchema.set('validateBeforeSave', false)

OfferSchema.pre('save', async function (next) {
	try {
		if (!this.uid) {
			const Model = this.constructor as Model<OfferDocument>
			let attempts = 0
			const maxAttempts = 5

			while (attempts < maxAttempts) {
				try {
					const result = await Model.aggregate([
						{
							$group: {
								_id: null,
								maxUid: { $max: '$uid' },
							},
						},
					]).exec()

					const newUid = result.length > 0 ? result[0].maxUid + 1 : 1

					// Check if this UID is already taken
					const existingDoc = await Model.findOne({ uid: newUid }).lean()
					if (!existingDoc) {
						this.uid = newUid
						break
					}

					attempts++
				} catch (err) {
					attempts++
					if (attempts === maxAttempts) {
						throw new Error(
							'Failed to generate unique UID after multiple attempts',
						)
					}
				}
			}
		}
		next()
	} catch (error) {
		next(error as Error)
	}
})

OfferSchema.index({
	priority: -1,
	dateExpiry: -1,
	trendingPriority: -1,
})

export { OfferSchema }
