import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { GiftCard } from './gift-card.entity'
import { User } from './user.entity'

@Schema({ timestamps: true })
export class IcbGiftCard {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid: number

	@Prop({
		type: Types.ObjectId,
		ref: 'GiftCard',
		required: true,
		unique: false,
	})
	giftCard: GiftCard | Types.ObjectId

	@Prop({ type: Number, required: true, unique: true })
	giftCardNumber: number

	@Prop({ type: String, required: true, unique: true })
	orderId: string

	@Prop({ type: Number, required: true })
	giftCardPin: number

	@Prop({ type: Number, required: false })
	mobile?: number

	@Prop({ type: String, required: true })
	email: string

	@Prop({ type: String, required: true })
	name: string

	@Prop({ type: String, required: false, default: 'ICB Gift Card' })
	msg: string

	@Prop({ type: Boolean, default: false })
	isRedeemed: boolean

	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	buyer: User | Types.ObjectId

	@Prop({ type: Types.ObjectId, ref: 'User', required: false })
	receiver: User | Types.ObjectId

	@Prop({ type: Number, required: true })
	amount: number

	@Prop({ type: Date, default: null })
	redeemDate: Date

	@Prop({ type: Date, required: true })
	expiryDate: Date

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy?: Admin
}

export type IcbGiftCardDocument = HydratedDocument<IcbGiftCard>
const IcbGiftCardSchema = SchemaFactory.createForClass(IcbGiftCard)

IcbGiftCardSchema.index(
	{ 'giftCard.uid': 1 },
	{ unique: true, partialFilterExpression: { 'giftCard.uid': { $ne: null } } },
)

IcbGiftCardSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}
	const icb = this.constructor as Model<IcbGiftCardDocument>
	const lastDocument = await icb.findOne().sort({ _id: -1 })
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	next()
})

export { IcbGiftCardSchema }
