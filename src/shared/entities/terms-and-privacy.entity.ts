import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'

@Schema({ timestamps: true })
export class TermsAndPrivacy {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: true })
	content!: string

	@Prop({ type: Boolean, default: true })
	active?: boolean

	@Prop({ type: String, enum: ['terms', 'privacy'], required: true })
	type?: 'terms' | 'privacy'

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin
}
export type TermsAndPrivacyDocument = HydratedDocument<TermsAndPrivacy>

export const TermsAndPrivacySchema =
	SchemaFactory.createForClass(TermsAndPrivacy)
