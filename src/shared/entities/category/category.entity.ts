import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin, AdminDocument } from '../admin.entity'
import { Image, ImageSchema } from '../image.entity'
import { SubCategory, SubCategoryDocument } from './sub-category.entity'

export type CategoryDocument = HydratedDocument<Category>

@Schema({ timestamps: true })
export class Category {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ required: true })
	name!: string

	@Prop({
		type: ImageSchema,
	})
	image!: Image

	@Prop({ default: false })
	trending!: boolean

	@Prop({ default: false })
	isTop!: boolean

	@Prop({ default: 1 })
	trendingPriority!: number

	@Prop({ default: 1 })
	priority!: number

	@Prop({ default: true })
	active!: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Number, default: 0 })
	oldId?: number
}

export const CategorySchema = SchemaFactory.createForClass(Category)
