import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Offer } from './offer.entity'
import { Store, StoreDocument } from './store.entity'
import { User } from './user.entity'

export type ReviewDocument = HydratedDocument<Review>

@Schema({ timestamps: true })
export class Review {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: Types.ObjectId, ref: 'Store', required: true })
	store: Store

	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	reviewer!: User

	@Prop({ type: Number, default: 1, enum: [1, 2, 3, 4, 5], required: true })
	rating: 1 | 2 | 3 | 4 | 5

	@Prop({ type: String })
	review: string

	@Prop({
		type: String,
		default: 'awaiting_to_approve',
		enum: ['approved', 'awaiting_to_approve', 'rejected'],
		required: true,
	})
	status: 'approved' | 'awaiting_to_approve' | 'rejected'

	@Prop({
		type: Boolean,
		default: true,
	})
	active!: boolean

	createdAt: string
}

export const ReviewSchema = SchemaFactory.createForClass(Review)

ReviewSchema.pre('save', async function (next) {
	const review = this.constructor as Model<ReviewDocument>
	const lastDocument = await review.findOne().sort({ _id: -1 })

	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	next()
})
