import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { Click } from './click.entity'
import { File, FileSchema } from './file.entity'
import { Store } from './store.entity'
import { User } from './user.entity'

export type MissingCashbackDocument = HydratedDocument<MissingCashback>

@Schema({ timestamps: true })
export class MissingCashback {
	@Prop({ type: Number, unique: true, index: true })
	uid!: number

	@Prop({
		type: Types.ObjectId,
		ref: 'Click',
		validate: {
			validator: function (v: Click) {
				return this.migrated ? true : v != null
			},
			message: 'Click is required',
		},
	})
	click!: Click

	@Prop({ type: String })
	complaintId: string

	@Prop({
		type: Types.ObjectId,
		ref: 'Store',
		validate: {
			validator: function (v: Store) {
				return this.migrated ? true : v != null
			},
			message: 'Store is required',
		},
	})
	store!: Store

	@Prop({ type: String, required: false })
	storeName!: string

	@Prop({
		type: FileSchema,
		validate: {
			validator: function (v: File) {
				return this.migrated ? true : v != null
			},
			message: 'Invoice is required',
		},
	})
	invoice?: File

	@Prop({ type: Types.ObjectId, ref: 'Affiliation', required: true })
	affiliation!: Affiliation

	@Prop({ type: String, required: false })
	coupon!: string

	@Prop({ type: String, required: false })
	orderId!: string

	@Prop({
		type: String,
		validate: {
			validator: function (v: string) {
				return this.migrated ? true : v != null
			},
			message: 'Message is required',
		},
	})
	message!: string

	@Prop({ type: Number, required: true })
	paidAmount!: number

	@Prop({ type: String, required: true, enum: ['new', 'old'] })
	userType!: 'new' | 'old'

	@Prop({
		type: String,
		required: true,
		enum: ['web', 'mobile', 'app'],
		default: 'web',
	})
	platform!: 'web' | 'mobile' | 'app'

	@Prop({
		type: String,
		required: true,
		enum: ['not-solved', 'solved', 'rejected', 'forwarded'],
		default: 'not-solved',
	})
	status!: 'not-solved' | 'solved' | 'rejected' | 'forwarded'

	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	user!: User

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Boolean, default: false })
	migrated?: boolean

	@Prop({ type: Number, default: 0 })
	oldId: number

	@Prop({ type: Date, required: true })
	orderDate!: Date

	createdAt!: Date
}

export const MissingCashbackSchema =
	SchemaFactory.createForClass(MissingCashback)

// index for search
MissingCashbackSchema.index({
	title: 'text',
	complaintId: 'text',
})
/*
MissingCashbackSchema.pre('save', async function (next) {
	try {
		if (!this.isNew) {
			return next()
		}
		const missingCashback = this.constructor as Model<MissingCashbackDocument>
		const lastDocument = await missingCashback.findOne().sort({ uid: -1 })
		if (lastDocument) {
			this.uid = lastDocument.uid + 1
		} else {
			this.uid = 1
		}
		if (!this.complaintId) {
			this.complaintId = `CBCOM${hash({
				uid: this.uid,
				user: this.user,
				orderId: this.orderId,
			}).toUpperCase()}`
		}

		next()
	} catch (e) {
		console.log(e)
	}
})
 */

MissingCashbackSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}
	const click = this.constructor as Model<MissingCashbackDocument>
	const lastDocument = await click.findOne().sort({ _id: -1 })
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	this.complaintId = `CBCLK${hash({
		uid: this.uid,
		user: this.user,
		orderId: this.orderId,
	}).toUpperCase()}`
	next()
})
