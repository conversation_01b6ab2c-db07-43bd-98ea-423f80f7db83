import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Store } from 'express-session'
import { HydratedDocument, Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Admin } from './admin.entity'
import { User } from './user.entity'

export type PaymentRequestDocument = HydratedDocument<PaymentRequest>

export class BankDetails {
	@Prop({ type: String, required: true })
	accountNumber: string

	@Prop({ type: String, required: true })
	IFSC: string

	@Prop({ type: String, required: true })
	bankName: string

	@Prop({ type: String, required: true })
	branchName: string

	@Prop({ type: String, required: true })
	accountHolderName: string
}

export class RechargeDetails {
	@Prop({ type: String, required: true })
	serviceProvider: string

	@Prop({ type: String, required: true })
	connectionType: string

	@Prop({ type: String, required: true })
	phoneNumber: string
}

export class GiftCardDetails {
	@Prop({ type: String, required: true })
	gvStore: string
}

@Schema({ timestamps: true })
export class PaymentRequest {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: false })
	ipAddress: string

	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	withdrawer!: User | Types.ObjectId

	@Prop({ type: String, required: true, unique: true, index: true })
	referenceId!: string

	@Prop({ type: Number, required: true })
	withdrawAmount: number

	@Prop({ type: Date })
	withdrawDate: Date

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({
		type: String,
		enum: ['bank', 'upi', 'giftVoucher', 'recharge', 'wallet'],
		required: true,
	})
	paymentType: 'bank' | 'upi' | 'giftVoucher' | 'recharge' | 'wallet'

	@Prop({
		type: String,
		required: function (this: PaymentRequest) {
			return this.paymentType === 'recharge'
		},
	})
	mobileNumber: string

	@Prop({
		type: GiftCardDetails,
		required: function (this: PaymentRequest) {
			return this.paymentType === 'giftVoucher'
		},
	})
	giftCardDetails: GiftCardDetails

	@Prop({
		type: RechargeDetails,
		required: function (this: PaymentRequest) {
			return this.paymentType === 'recharge'
		},
	})
	rechargeDetails: RechargeDetails

	// @Prop({
	// 	type: BankDetails, required: function (this: PaymentRequest) {
	// 		return this.paymentType === 'bank'
	// 	}
	// })
	// bankDetails: BankDetails

	@Prop({
		type: String,
		default: 'pending',
		enum: ['approved', 'pending', 'rejected'],
		required: true,
	})
	status: 'approved' | 'pending' | 'rejected'

	@Prop({ type: String, required: false })
	notes: string

	@Prop({ type: Date })
	requestedDate: Date

	updatedAt: Date

	@Prop({ type: Number })
	oldId: number
}

export const PaymentRequestSchema = SchemaFactory.createForClass(PaymentRequest)

PaymentRequestSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}
	const payment = this.constructor as Model<PaymentRequestDocument>
	const lastDocument = await payment.findOne().sort({ uid: -1 })
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	this.referenceId = `PYMT${hash({
		uid: this.uid,
		user: this.withdrawer.toString(),
		time: new Date().getTime(),
	}).toUpperCase()}`
	next()
})
