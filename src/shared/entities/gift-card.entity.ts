import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Categories, CategoriesSchema } from './category'
import { Image, ImageSchema } from './image.entity'
import { Store, StoreDocument } from './store.entity'

@Schema({ timestamps: true })
export class GiftCard {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid: number

	@Prop({ type: String, required: true })
	name: string

	@Prop({
		type: ImageSchema,
	})
	logo: Image

	@Prop({
		type: ImageSchema,
	})
	image: Image

	@Prop({
		type: [Types.ObjectId],
		required: true,
		ref: 'Categories',
	})
	categories!: Categories[]

	@Prop([{ type: Types.ObjectId, ref: 'GiftCard' }])
	relatedGiftCards: GiftCard[]

	@Prop({ type: Number })
	discountGetting?: number

	@Prop({ type: Number, min: 0, max: 100, required: true, index: true })
	cashbackGiving: number

	@Prop({ type: Date, required: true })
	validity: Date

	@Prop({ type: String })
	notes?: string

	@Prop({ type: [String], required: true })
	howToUse: string[]

	@Prop({ type: String, required: true })
	terms: string

	@Prop({ type: String, required: true })
	description: string

	@Prop({ type: Number, default: 0 })
	priority: number

	@Prop({ type: String })
	qwickcilverId?: string

	@Prop({ type: String })
	tncMail?: string

	@Prop({
		type: ImageSchema,
	})
	qwickcilverImage?: Image

	@Prop({ type: [Number] })
	denominations: number[]

	@Prop({ type: Boolean, default: false })
	isCustomDenomination: boolean

	@Prop({ type: String, enum: ['icb', 'qwickcilver'], default: 'icb' })
	cardType: 'icb' | 'qwickcilver'

	@Prop({ type: String, enum: ['yes', 'no'], default: 'no' })
	refresh: 'yes' | 'no'

	@Prop({ type: Number, required: true })
	minimumAmount: number

	@Prop({ type: Number, required: true })
	maximumAmount: number

	@Prop([{ type: Types.ObjectId, ref: 'Store' }])
	relatedCbStore: StoreDocument

	@Prop({ type: Types.ObjectId, ref: 'Store' })
	relatedInstantStore?: StoreDocument

	@Prop({ type: String })
	provider?: string

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin', required: true })
	createdBy: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy?: Admin
}

export type GiftCardDocument = HydratedDocument<GiftCard>
const GiftCardSchema = SchemaFactory.createForClass(GiftCard)

GiftCardSchema.index({
	cashbackGiving: -1,
})

export { GiftCardSchema }
