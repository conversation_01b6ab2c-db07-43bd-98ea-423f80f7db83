import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { StoreCategory } from './category'
import { Click } from './click.entity'
import { Offer } from './offer.entity'
import { Store } from './store.entity'
import { User } from './user.entity'

export type EarningDocument = HydratedDocument<Earning>

@Schema({ timestamps: true })
export class Earning {
	@Prop({ required: true, unique: true, index: true })
	uid: number

	@Prop({ required: true, unique: true, index: true })
	referenceId: string

	@Prop({ type: Types.ObjectId, required: false, ref: 'Offer' })
	offer: Offer

	@Prop({ type: Types.ObjectId, required: false, ref: 'Store' })
	store: Store

	@Prop({ type: Types.ObjectId, required: false, ref: 'Click' })
	click: Click

	@Prop({ type: Types.ObjectId, required: false, ref: 'StoreCategory' })
	storeCategory: StoreCategory

	@Prop({ type: Types.ObjectId, required: true, ref: 'User' })
	user: User

	@Prop({ type: Types.ObjectId, required: false, ref: 'User' })
	referralUser: User

	@Prop({ type: Types.ObjectId, required: false, ref: 'Affiliation' })
	affiliation: Affiliation

	@Prop({ type: Number, default: 0 })
	cashbackAmount: number

	@Prop({ type: Number, default: 0 })
	referralCommission: number

	@Prop({ type: Number, default: 0 })
	amountGot: number

	@Prop({ type: Number, default: 0 })
	amountPromised: number

	@Prop({ type: Number, default: 0 })
	saleAmount: number

	@Prop({ type: Number, default: 0 })
	percentGot: number

	@Prop({ type: String })
	notes: string

	@Prop({ type: String })
	offerFromPartner: string

	@Prop({ type: String })
	categoryPercentOrAmount: string

	@Prop({ type: String })
	orderUniqueId: string

	@Prop({ type: String })
	advertiserInfo: string

	@Prop({ type: String })
	otherInfo: string

	@Prop({ type: String })
	remarks: string

	@Prop({ type: Number })
	orderCount: number

	@Prop({ type: Date })
	confirmDate: Date

	@Prop({ type: Date })
	dateConfirmedCancelled: Date

	@Prop({ type: Boolean, default: false })
	autoUpdated: boolean

	@Prop({ type: Date })
	trackingTime: Date

	@Prop({
		type: String,
		enum: [
			'pending',
			'tracked_for_confirm',
			'tracked_for_cancel',
			'confirmed',
			'cancelled',
		],
		default: 'pending',
	})
	status:
		| 'pending'
		| 'tracked_for_confirm'
		| 'tracked_for_cancel'
		| 'confirmed'
		| 'cancelled'

	@Prop({
		type: String,
		enum: ['click', 'missing', 'referral'],
		default: 'click',
	})
	earningsType: 'click' | 'missing' | 'referral'

	@Prop({ type: Types.ObjectId, required: false, ref: 'Admin' })
	createdBy: Admin

	@Prop({ type: Types.ObjectId, required: false, ref: 'Admin' })
	updatedBy: Admin

	@Prop({ type: Number, default: 0 })
	oldId: number

	@Prop({ type: Boolean, default: false })
	migrated?: boolean
}

export const EarningSchema = SchemaFactory.createForClass(Earning)

EarningSchema.pre('save', async function (next) {
	const userEarning = this.constructor as Model<EarningDocument>
	const lastDocument = await userEarning.findOne().sort({ uid: -1 }).exec()
	console.log('🚀 ~ lastDocument:', lastDocument)
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	this.referenceId = `CBERN${hash({
		uid: this.uid,
		user: this.user,
		store: this.store,
		affiliation: this.affiliation,
		addedBy: this.createdBy,
		createdAt: new Date(),
	}).toUpperCase()}`
	next()
})
