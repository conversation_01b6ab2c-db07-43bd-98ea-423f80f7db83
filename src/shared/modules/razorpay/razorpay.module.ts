import { Module } from '@nestjs/common'
import { env } from 'config'
import { RazorpayModule as Razorpay } from 'nestjs-razorpay'
import { RazorpayService } from './razorpay.service'

@Module({
	imports: [
		Razorpay.forRoot({
			// biome-ignore lint/style/useNamingConvention: <explanation>
			key_id: env.RAZORPAY.keyId,
			// biome-ignore lint/style/useNamingConvention: <explanation>
			key_secret: env.RAZORPAY.keySecret,
		}),
	],
	providers: [RazorpayService],
	exports: [RazorpayService], // Export to use in other modules
})
export class RazorpayModule {}
