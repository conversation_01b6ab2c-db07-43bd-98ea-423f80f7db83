import { OrderGiftCardDto } from 'app/gift-card/dto/order-giftcard.dto'
import { Orders } from 'razorpay/dist/types/orders'
import { ReqUser } from 'shared/entities'
import { PaymentType } from 'shared/enums'
import { RazorpayService } from '../razorpay/razorpay.service'
import { PaymentProcessor } from './payment-processor'

export class RazorpayPaymentProcessor implements PaymentProcessor {
	type: PaymentType = 'razorpay'
	constructor(private razorpayService: RazorpayService) {}

	async processPayment(
		user: ReqUser,
		amount: number,
		orderData: OrderGiftCardDto,
	): Promise<{
		amount: number
		order: Orders.RazorpayOrder
		status: 'pending'
	}> {
		const order = await this.razorpayService.createOrder({
			amount: amount,
			currency: 'INR',
			receipt: `rcpt_${new Date().getTime()}`,
			notes: {
				userId: user.id.toString(),
				giftCardId: orderData.giftcardId.toString(),
			},
		})
		return {
			amount: amount,
			order,
			status: 'pending',
		}
	}
}
