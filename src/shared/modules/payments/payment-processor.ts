import { OrderGiftCardDto } from 'app/gift-card/dto/order-giftcard.dto'
import { Orders } from 'razorpay/dist/types/orders'
import { ReqUser } from 'shared/entities'
import { PaymentType } from 'shared/enums'

export interface PaymentProcessor {
	type: PaymentType
	processPayment(
		user: ReqUser,
		amount: number,
		orderData: OrderGiftCardDto,
	): Promise<{
		amount: number
		order: Orders.RazorpayOrder | null
		status: 'success' | 'pending'
	}>
}
