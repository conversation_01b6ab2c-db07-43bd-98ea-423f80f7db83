export function normalizeGmail(email: string): string {
	const [local, domain] = email.toLowerCase().split('@')
	return domain === 'gmail.com'
		? `${local?.replace(/\./g, '')}@${domain}`
		: email.toLowerCase()
}

export function isFakeGmailAlias(newMail: string, existing: string): boolean {
	const normalize = (email: string): string => {
		const [local, domain] = email.toLowerCase().split('@')
		if (domain === 'gmail.com') {
			return `${local?.replace(/\./g, '').split('+')[0]}@${domain}`
		}
		return email.toLowerCase()
	}

	return normalize(newMail) === normalize(existing)
}
