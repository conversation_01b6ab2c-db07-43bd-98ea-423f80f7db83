import { env } from '@config'
import { ofetch } from 'ofetch'

interface EmailData {
	[key: string]: any
}

interface SendEmailOptions {
	subscriberEmail: string
	templateId: number
	data: EmailData
}
// t - 10 - Signup Welcome - data:{name,amount}
// t - 11 - Referral Welcome - data:{referrer_name,referred_name,amount}

export async function sendTransactionalEmail({
	subscriberEmail,
	templateId,
	data,
}: SendEmailOptions): Promise<void> {
	try {
		await ofetch('/api/tx', {
			method: 'POST',
			// biome-ignore lint/style/useNamingConvention: <explanation>
			baseURL: env.LISTMONK.baseUrl,
			headers: {
				'Content-Type': 'application/json',
				// biome-ignore lint/style/useNamingConvention: <explanation>
				Authorization: `Basic ${btoa(`${env.LISTMONK.username}:${env.LISTMONK.password}`)}`,
			},
			body: {
				// biome-ignore lint/style/useNamingConvention: <explanation>
				subscriber_email: subscriberEmail,
				// biome-ignore lint/style/useNamingConvention: <explanation>
				template_id: templateId,
				data,
				// biome-ignore lint/style/useNamingConvention: <explanation>
				content_type: 'html',
			},
			onResponseError({ response }) {
				throw new Error(`Failed to send email: ${response._data.message}`)
			},
		})
		console.log(
			`Email sent to ${subscriberEmail} using template ID ${templateId}`,
		)
	} catch (error) {
		console.error('Failed to send email:', error)
	}
}

interface SubscriberAttribs {
	[key: string]: string | number | boolean | string[] | Record<string, unknown>
}

interface CreateSubscriberOptions {
	email: string
	name: string
	referralCode: string
	status?: string
}

export async function createSubscriber({
	email,
	name,
	referralCode,
	status,
}: CreateSubscriberOptions) {
	try {
		await ofetch('/api/subscribers', {
			method: 'POST',
			// biome-ignore lint/style/useNamingConvention: <explanation>
			baseURL: env.LISTMONK.baseUrl,
			headers: {
				'Content-Type': 'application/json',
				// biome-ignore lint/style/useNamingConvention: <explanation>
				Authorization: `Basic ${btoa(`${env.LISTMONK.username}:${env.LISTMONK.password}`)}`,
			},
			body: {
				email: email,
				name: name,
				referralCode: referralCode,
				status: status || 'enabled',
			},
			onResponseError({ response }) {
				console.error(`Failed to create subscriber: ${response._data.message}`)
			},
		})
		console.log(`Subscriber created for ${email}`)
	} catch (error) {
		console.error('Failed to create subscriber:', error)
	}
}
