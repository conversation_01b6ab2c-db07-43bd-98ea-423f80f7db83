import { Module, forwardRef } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { GiftCardModule } from 'app/gift-card/gift-card.module'
import { StoreModule } from 'app/store/store.module'
import { UserModule } from 'app/user/user.module'
import { SavedItem, SavedItemSchema } from 'shared/entities/saved-item.entity'
import { SavedItemController } from './saved-item.controller'
import { SavedItemService } from './saved-item.service'

@Module({
	imports: [
		UserModule,
		MongooseModule.forFeature([
			{
				name: SavedItem.name,
				schema: SavedItemSchema,
			},
		]),
	],
	controllers: [SavedItemController],
	providers: [SavedItemService],
	exports: [SavedItemService],
})
export class SavedItemModule {}
