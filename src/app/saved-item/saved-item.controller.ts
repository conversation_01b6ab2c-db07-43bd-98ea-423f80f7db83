import { Controller, Get, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { GetGiftCardListResponse } from 'app/gift-card/types/get-gift-card.types'
import { GetAllStoresDto } from 'app/store/dto/get-all-stores.dto'
import { GetAllStoresResponse } from 'app/store/types/get-all-stores.types'
import { Auth } from 'shared/decorators'
import { User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { GetSavedGiftCardsDto, GetSavedOffersDto } from './dto/saved-item.dto'
import { SavedItemService } from './saved-item.service'
import {
	SavedCouponsResponse,
	SavedDealsResponse,
	SavedOfferUidsResponse,
} from './types/saved-item.types'

@ApiTags('SavedItems')
@Controller('saved-items')
export class SavedItemController {
	constructor(private readonly saveService: SavedItemService) {}

	@ApiResponse({
		type: SavedDealsResponse,
	})
	@Auth()
	@Get('/deals')
	async getAllSavedDeals(
		@Query() queryParams: GetSavedOffersDto,
		@User() user: ReqUser,
	) {
		return await this.saveService.getAllSavedDeals(queryParams, user)
	}

	@ApiResponse({
		type: [Number],
	})
	@Auth()
	@Get('/saved-offer-ids')
	async getAllSavedOfferUids(@User() user: ReqUser) {
		return await this.saveService.getAllSavedOfferIds(user)
	}

	@ApiResponse({
		type: SavedCouponsResponse,
	})
	@Auth()
	@Get('/coupons')
	async getAllSavedCoupons(
		@Query() queryParams: GetSavedOffersDto,
		@User() user: ReqUser,
	) {
		return await this.saveService.getAllSavedCoupons(queryParams, user)
	}

	@ApiResponse({
		type: GetAllStoresResponse,
	})
	@Auth()
	@Get('/stores')
	async getAllSavedStores(
		@Query() queryParams: GetAllStoresDto,
		@User() user: ReqUser,
	) {
		return await this.saveService.getAllSavedStores(queryParams, user)
	}

	@ApiResponse({
		type: GetGiftCardListResponse,
	})
	@Auth()
	@Get('/gift-cards')
	async getAllSavedGiftCards(
		@Query() queryParams: GetSavedGiftCardsDto,
		@User() user: ReqUser,
	) {
		return await this.saveService.getAllSavedGiftCards(queryParams, user)
	}
}
