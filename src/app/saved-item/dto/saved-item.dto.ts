import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { OfferTypes, SortTypes, StoreSortTypes, UserTypes } from 'shared/enums'
import { SavedEnum } from 'shared/types'

export class CreateSaveItemDto {
	@IsNumber()
	@ApiProperty({
		example: 23,
	})
	itemUid: number

	@IsEnum(SavedEnum)
	@IsOptional()
	@ApiProperty({ enum: SavedEnum, enumName: 'SavedEnum' })
	itemType!: SavedEnum
}

export class RemoveSavedItemDto {
	@IsNumber()
	@ApiProperty({
		example: 23,
	})
	itemUid: number

	@IsEnum(SavedEnum)
	@IsOptional()
	@ApiProperty({ enum: SavedEnum, enumName: 'SavedEnum' })
	itemType!: SavedEnum
}

export class GetSavedItemsDto {
	@IsEnum(SavedEnum)
	@ApiProperty({ enum: SavedEnum, enumName: 'SavedEnum' })
	itemType?: SavedEnum
}

export class GetSavedOffersDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsEnum(UserTypes)
	@IsOptional()
	@ApiProperty({ enum: UserTypes, enumName: 'UserTypes' })
	userType?: UserTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return undefined
	}
}

export class GetSavedGiftCardsDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return undefined
	}
}

export class GetSavedStoresDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return undefined
	}
}
