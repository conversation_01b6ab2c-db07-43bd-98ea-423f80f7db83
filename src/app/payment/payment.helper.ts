import { Types } from 'mongoose'
import { UserDocument } from 'shared/entities'
import { PaymentTypes } from 'shared/enums'
import { PaymentStatus, PaymentStatusType } from 'shared/types'

export function buildPaymentAggregateQuery({
	loggedUser,
	status,
	paymentType,
	paymentStartDate,
	paymentEndDate,
	queryConditions,
	sortQuery,
	skip,
	limit,
}: {
	loggedUser: UserDocument
	status: string
	paymentType: PaymentTypes
	paymentStartDate: string | undefined
	paymentEndDate: string | undefined
	queryConditions?: string
	sortQuery?: Record<string, 1 | -1>
	skip?: number
	limit?: number
}) {
	console.log('🚀 ~ status:', status)
	function getStatusList(statusValue: string) {
		switch (statusValue) {
			case PaymentStatus.paid:
				return [PaymentStatus.paid]
			case PaymentStatus.requested:
				return [PaymentStatus.requested]
			case PaymentStatus.cancelled:
				return [PaymentStatus.cancelled]
			default:
				return [PaymentStatus.requested]
		}
	}
	function getPaymentList(paymentType: string) {
		switch (paymentType) {
			case PaymentTypes.Bank:
				return [PaymentTypes.Bank]
			case PaymentTypes.Upi:
				return [PaymentTypes.Upi]
			default:
				return [PaymentTypes.Bank]
		}
	}
	const aggregationPipeline = []

	aggregationPipeline.push({
		$match: {
			withdrawer: new Types.ObjectId(loggedUser._id),
		},
	})

	// Match stage for status
	if (status) {
		aggregationPipeline.push({
			$match: {
				status: { $in: status },
			},
		})
	}
	if (queryConditions) {
		aggregationPipeline.push({
			$match: {
				referenceId: { $regex: queryConditions ?? '', $options: 'i' },
			},
		})
	}

	// Match stage for payment type
	if (paymentType) {
		aggregationPipeline.push({
			$match: {
				paymentType: { $in: getPaymentList(paymentType) },
			},
		})
	}

	if (paymentStartDate && paymentEndDate) {
		// Convert IST start date to UTC
		const utcStartDate = new Date(paymentStartDate)
		const utcStartDateString = utcStartDate.toISOString()

		// Convert IST end date to UTC
		const utcEndDate = new Date(paymentEndDate)
		const utcEndDateString = utcEndDate.toISOString()
		aggregationPipeline.push({
			$match: {
				$expr: {
					$and: [
						{
							$gte: [
								{
									$dateToString: { format: '%Y-%m-%d', date: '$withdrawDate' },
								},
								utcStartDateString,
							],
						},
						{
							$lte: [
								{
									$dateToString: { format: '%Y-%m-%d', date: '$withdrawDate' },
								},
								utcEndDateString,
							],
						},
					],
				},
			},
		})
	}

	// Filter pipeline for facet stage
	const filterPipeline = []

	if (sortQuery) {
		filterPipeline.push({ $sort: sortQuery })
	}
	// Skip stage
	if (skip) {
		filterPipeline.push({ $skip: skip })
	}
	// Limit stage
	if (limit) {
		filterPipeline.push({ $limit: limit })
	}
	// Project stage
	filterPipeline.push({
		$project: {
			uid: 1,
			referenceId: 1,
			withdrawAmount: 1,
			paymentType: 1,
			status: 1,
			withdrawDate: 1,
			requestedDate: 1,
		},
	})

	// Facet stage
	aggregationPipeline.push({
		$facet: {
			paginatedResults: filterPipeline,
			totalCount: [{ $count: 'count' }],
		},
	})

	return aggregationPipeline
}
