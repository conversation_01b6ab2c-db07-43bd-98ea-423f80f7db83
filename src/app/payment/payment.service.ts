import { format } from '@formkit/tempo'
import {
	BadRequestException,
	Injectable,
	NotFoundException,
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import { ReqUser, UserDocument } from 'shared/entities'
import {
	PaymentRequest,
	PaymentRequestDocument,
} from 'shared/entities/payment-request.entity'
import { buildPaymentSortQuery } from 'shared/helpers/query-builder'
import { convertLocalToUTC } from 'shared/helpers/time.helper'
import { Payment, PaymentStatus } from 'shared/types'
import { GetPaymentRequestDto, PaymentRequestDto } from './dto/payment.dto'
import { buildPaymentAggregateQuery } from './payment.helper'
import { GetPaymentListResponse } from './types/payment.types'

@Injectable()
export class PaymentService {
	constructor(
		@InjectModel(PaymentRequest.name)
		private paymentRequest: Model<PaymentRequestDocument>,
		private readonly userService: UserService,
	) {}

	async requestPayments(paymentData: PaymentRequestDto, loggedUser: ReqUser) {
		const payment = await this.userService.withdrawBalance(
			loggedUser.id,
			paymentData.withdrawAmount,
			paymentData.paymentType,
		)

		if (payment) {
			const paymentRequest = new this.paymentRequest({
				uid: 0,
				referenceId: 'PYMT',
				withdrawer: new Types.ObjectId(loggedUser.id),
				withdrawAmount: paymentData.withdrawAmount,
				paymentType: paymentData.paymentType,
				status: 'pending',
				requestedDate: convertLocalToUTC(),
			})
			await paymentRequest.save()
		}

		return true
	}
	async getAllPaymentRequestedUser(
		queryParams: GetPaymentRequestDto,
		userData: ReqUser,
	): Promise<GetPaymentListResponse> {
		const page = queryParams.page ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const limit = queryParams.pageSize
		const status = queryParams.status
		const paymentType = queryParams.paymentType
		const paymentStartDate = queryParams.startDate
		const paymentEndDate = queryParams.endDate
		const queryConditions = queryParams.searchParam

		// Match stage for user referral

		const loggedUser = (await this.userService.getUserByEmail(
			userData.email,
		)) as UserDocument

		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}

		const sortQuery = buildPaymentSortQuery(
			queryParams.sortType,
			'requestedDate',
			'withdrawAmount',
		)

		const aggregationPipeline = buildPaymentAggregateQuery({
			loggedUser,
			status,
			paymentType,
			paymentStartDate,
			paymentEndDate,
			queryConditions,
			sortQuery,
			skip,
			limit,
		})

		const aggregationResult = await this.paymentRequest
			.aggregate(aggregationPipeline)
			.exec()

		if (!aggregationResult || aggregationResult.length === 0) {
			throw new NotFoundException('Payment History list not found')
		}

		const [result] = aggregationResult
		function formatTimeStamp(date: string) {
			const utcTimestamp = Date.parse(date)
			const offset = 5.5 * 60 * 60 * 1000
			const istTimestamp = utcTimestamp + offset
			return istTimestamp
		}
		const paymentRequests = result.paginatedResults as PaymentRequestDocument[]
		const totalCount =
			result.totalCount.length > 0 ? result.totalCount[0].count : 0
		return {
			payments: paymentRequests.map(paymentRequest => {
				const {
					withdrawAmount,
					requestedDate,
					paymentType,
					status,
					referenceId,
				} = paymentRequest

				return {
					referenceId,
					withdrawAmount,
					paymentType:
						paymentType === 'bank'
							? Payment.bank
							: paymentType === 'upi'
								? Payment.upi
								: paymentType === 'giftVoucher'
									? Payment.giftVoucher
									: paymentType === 'recharge'
										? Payment.recharge
										: Payment.wallet,
					status:
						status === 'pending'
							? PaymentStatus.requested
							: status === 'approved'
								? PaymentStatus.paid
								: PaymentStatus.cancelled,
					paymentDate: formatTimeStamp(requestedDate as unknown as string),
				}
			}),
			pagination: {
				page,
				pageSize: paymentRequests.length,
				total: totalCount,
			},
		}
	}
}
