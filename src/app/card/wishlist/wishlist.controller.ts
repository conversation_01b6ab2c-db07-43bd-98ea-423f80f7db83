import {
	Body,
	Controller,
	Get,
	Post,
	Query,
	UploadedFile,
} from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { WishlistEmailDto, WishlistResponseDto } from './dto/wishlist.dto'
import { WishListService } from './wishlist.service'

@ApiTags('WishList')
@Controller('card/wishlist')
export class WishListController {
	constructor(private readonly wishlistService: WishListService) {}

	@ApiResponse({
		type: WishlistResponseDto,
	})
	@Post('join')
	async joinWishlist(@Body() wishlistData: WishlistEmailDto) {
		return await this.wishlistService.addToWishList(wishlistData.email)
	}
}
