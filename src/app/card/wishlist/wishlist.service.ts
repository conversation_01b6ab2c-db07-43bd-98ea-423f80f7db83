import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { CardWishList, CardWishListDocument } from './schema/wishlist.schema'

@Injectable()
export class WishListService {
	constructor(
		@InjectModel(CardWishList.name)
		private wishlist: Model<CardWishListDocument>,
	) {}

	async addToWishList(email: string) {
		if (!email) {
			return { message: 'Email is required' }
		}
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
		if (!emailRegex.test(email)) {
			return { message: 'Invalid email format' }
		}
		const existingWishlist = await this.wishlist.findOne({ email }).exec()
		if (existingWishlist) {
			return { message: 'Already in wishlist' }
		}

		const newWishlist = new this.wishlist({ email })
		await newWishlist.save()
		return { message: 'Added to wishlist' }
	}
}
