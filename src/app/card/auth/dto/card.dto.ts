import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNumberString, Length } from 'class-validator'

export class CardLoginDto {
	@ApiProperty({
		example: '1234567890',
		description: 'User number for login',
		required: false,
	})
	mobile!: string
}

export class CardLoginResponseDto {
	@ApiProperty({
		example: 'Logged in successfully',
		description: 'Response message',
	})
	message!: string
}
