import { <PERSON><PERSON>le, OnModuleInit } from '@nestjs/common'
import { InjectModel, MongooseModule } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import {
	Admin,
	AdminSchema,
	Affiliation,
	AffiliationSchema,
	Banner,
	BannerSchema,
	Category,
	CategorySchema,
	Click,
	ClickSchema,
	GiftCard,
	GiftCardDocument,
	GiftCardSchema,
	MobileStory,
	MobileStorySchema,
	Offer,
	OfferDocument,
	OfferSchema,
	PersonalInterest,
	PersonalInterestSchema,
	QuickAccess,
	QuickAccessSchema,
	Store,
	StoreCategory,
	StoreCategorySchema,
	StoreDocument,
	StoreSchema,
	SubCategory,
	SubCategorySchema,
	TermsAndPrivacy,
	TermsAndPrivacySchema,
	User,
	UserSchema,
} from 'shared/entities'
import { Earning, EarningSchema } from 'shared/entities/earning.entity'
import {
	HeroAccess,
	HeroAccessSchema,
} from 'shared/entities/hero-access.entity'
import { IcbGiftCard, IcbGiftCardSchema } from 'shared/entities/icb-gift-card'
import {
	MissingCashback,
	MissingCashbackSchema,
} from 'shared/entities/missing-cashback.entity'
import {
	OngoingSaleOffers,
	OngoingSaleOffersSchema,
} from 'shared/entities/ongoing-sale-offers.entity'
import { Review, ReviewSchema } from 'shared/entities/review.entity'
import {
	Testimonial,
	TestimonialSchema,
} from 'shared/entities/testimonial.entity'
import { SeedController } from './seed.controller'
import { SeedService } from './seed.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: Admin.name,
				schema: AdminSchema,
			},
			{
				name: Affiliation.name,
				schema: AffiliationSchema,
			},
			{
				name: Category.name,
				schema: CategorySchema,
			},
			{
				name: SubCategory.name,
				schema: SubCategorySchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: HeroAccess.name,
				schema: HeroAccessSchema,
			},
			{
				name: MobileStory.name,
				schema: MobileStorySchema,
			},
			{
				name: StoreCategory.name,
				schema: StoreCategorySchema,
			},
			{
				name: Offer.name,
				schema: OfferSchema,
			},
			{
				name: HeroAccess.name,
				schema: HeroAccessSchema,
			},
			{
				name: QuickAccess.name,
				schema: QuickAccessSchema,
			},
			{
				name: Banner.name,
				schema: BannerSchema,
			},
			{
				name: OngoingSaleOffers.name,
				schema: OngoingSaleOffersSchema,
			},
			{
				name: GiftCard.name,
				schema: GiftCardSchema,
			},
			{
				name: User.name,
				schema: UserSchema,
			},
			{
				name: Click.name,
				schema: ClickSchema,
			},
			{
				name: Earning.name,
				schema: EarningSchema,
			},
			{
				name: Review.name,
				schema: ReviewSchema,
			},
			{
				name: Testimonial.name,
				schema: TestimonialSchema,
			},
			{
				name: PersonalInterest.name,
				schema: PersonalInterestSchema,
			},
			{
				name: TermsAndPrivacy.name,
				schema: TermsAndPrivacySchema,
			},
			{
				name: IcbGiftCard.name,
				schema: IcbGiftCardSchema,
			},
			{
				name: MissingCashback.name,
				schema: MissingCashbackSchema,
			},
		]),
	],
	controllers: [SeedController],
	providers: [SeedService],
})
export class SeedModule {
	// constructor(
	// 	@InjectModel(Store.name) private readonly store: Model<StoreDocument>,
	// 	@InjectModel(Offer.name) private readonly offer: Model<OfferDocument>,
	// 	@InjectModel(GiftCard.name)
	// 	private readonly giftCard: Model<GiftCardDocument>,
	// ) {}
	// async onModuleInit() {
	// 	console.log('Creating indexes')
	// 	await this.store.createCollection()
	// 	await this.offer.createCollection()
	// 	await this.giftCard.createCollection()
	// }
}
