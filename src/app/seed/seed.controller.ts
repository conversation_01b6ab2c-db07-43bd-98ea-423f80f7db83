import { Body, Controller, Param, Post } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { SeedService } from './seed.service'

@ApiTags('Seed')
@Controller('seed')
export class SeedController {
	constructor(private readonly seedService: SeedService) {}

	@Post()
	seed() {
		return this.seedService.seed()
	}

	@Post('referrals:email')
	seedReferral(@Param('email') email: string) {
		return this.seedService.seedReferral(email)
	}

	@Post('missing-cashbacks:email')
	seedMissingCashback(@Param('email') email: string) {
		return this.seedService.seedMissingCashback(email)
	}

	@Post('meilisearch')
	seedMeiliSearch() {
		return this.seedService.seedMeiliSearch()
	}
}
