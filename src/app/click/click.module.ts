import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { OfferModule } from 'app/offer/offer.module'
import { StoreCategoryModule } from 'app/store-category/store-category.module'
import { StoreModule } from 'app/store/store.module'
import { UserModule } from 'app/user/user.module'
import { Click, ClickSchema, Store } from 'shared/entities'
import { ClickController } from './click.controller'
import { ClickService } from './click.service'
import { MissingCashbackModule } from './missing-cashback/missing-cashback.module'

@Module({
	imports: [
		UserModule,
		OfferModule,
		StoreModule,
		StoreCategoryModule,
		MongooseModule.forFeature([
			{
				name: Click.name,
				schema: ClickSchema,
			},
		]),
	],
	exports: [ClickService],
	controllers: [ClickController],
	providers: [ClickService],
})
export class ClickModule {}
