import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import {
	IsArray,
	IsEnum,
	IsNotEmpty,
	IsNumber,
	IsOptional,
	IsString,
	<PERSON>,
	<PERSON>,
} from 'class-validator'
import { IsOptionalEnum } from 'shared/decorators'
import { PaginationDto } from 'shared/dto'
import { SortTypes } from 'shared/enums'

class DateFilter {
	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiProperty({ example: '2021-10-10', required: false })
	startDate!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiProperty({ example: '2021-10-10', required: false })
	endDate!: string
}
export class GetClicksDto extends PaginationDto {
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'and',
		required: false,
	})
	searchParam!: string

	@IsOptionalEnum(SortTypes)
	@ApiPropertyOptional({
		enum: SortTypes,
		enumName: 'SortTypes',
		required: false,
	})
	sortType: SortTypes = SortTypes.Newest

	@IsOptional()
	@Transform(({ value }) => {
		if (value === '') {
			throw new Error('stores cannot be an empty string')
		}
		return value.split(',').map((val: string) => Number.parseInt(val, 10))
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: '1,2,3',
		type: String,
		required: false,
	})
	stores!: number[]

	@IsOptional()
	@Transform(({ value }) => value.split(','))
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'Pending,Tracked,Confirmed,Cancelled,Report Missing CB',
		type: String,
		required: false,
	})
	status!: string

	@IsOptional()
	@IsString()
	@ApiProperty({ example: '2021-10-10', required: false })
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	startDate!: string

	@IsOptional()
	@IsString()
	@ApiProperty({ example: '2021-10-10', required: false })
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	endDate!: string
}
export class GetClicksByStoreDto extends PaginationDto {
	@IsOptional()
	@IsString()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'and',
		required: false,
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsNotEmpty()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes', required: false })
	sortType!: SortTypes

	@IsOptional()
	@Transform(({ value }) => {
		if (value === '') {
			throw new Error('stores cannot be an empty string')
		}
		return value.split(',').map((val: string) => Number.parseInt(val, 10))
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: '1,2,3',
		type: String,
		required: false,
	})
	stores!: number[]

	@IsOptional()
	@Transform(({ value }) => value.split(','))
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'Pending,Tracked,Confirmed,Cancelled,Report Missing CB',
		type: String,
		required: false,
	})
	status!: string

	@IsOptional()
	@IsString()
	@IsNotEmpty()
	@ApiPropertyOptional({ example: '2021-10-10', required: false })
	date!: string
}
