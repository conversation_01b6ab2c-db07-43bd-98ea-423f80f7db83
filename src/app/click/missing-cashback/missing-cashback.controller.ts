import {
	Body,
	Controller,
	Get,
	Post,
	Query,
	UploadedFile,
} from '@nestjs/common'
import { ApiBody, ApiConsumes, ApiResponse, ApiTags } from '@nestjs/swagger'
import { Auth, AwsFileUpload, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { GetReportsDto } from './dto/get-reports'
import {
	MissingCashbackResponse,
	MissingCashbackType,
} from './dto/get-reports.response'
import { ReportMissingCashbackDto } from './dto/report.dto'
import { MissingCashbackService } from './missing-cashback.service'

@ApiTags('Click')
@Controller('click/missing-cashback')
export class MissingCashbackController {
	constructor(
		private readonly missingCashbackService: MissingCashbackService,
	) {}

	@Auth()
	@Post('report')
	@ApiResponse({
		type: String,
	})
	@ApiConsumes('multipart/form-data')
	@ApiBody({ type: ReportMissingCashbackDto })
	@AwsFileUpload('invoice')
	async reportMissingCashback(
		@User() user: ReqUser,
		@UploadedFile() imageFile: Express.Multer.File,
		@Body() payloadDto: ReportMissingCashbackDto,
	) {
		return this.missingCashbackService.reportMissingCashback(
			payloadDto.reportData,
			user,
			imageFile,
		)
	}

	@Auth()
	@Get('list')
	@ApiResponse({
		type: MissingCashbackResponse,
	})
	async listMissingCashback(
		@User() user: ReqUser,
		@Query() query: GetReportsDto,
	) {
		return this.missingCashbackService.listMissingCashback(user, query)
	}
}
