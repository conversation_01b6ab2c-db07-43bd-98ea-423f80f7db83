import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import {
	IsArray,
	IsEnum,
	IsNotEmpty,
	IsNumber,
	IsOptional,
	IsString,
	<PERSON>,
	<PERSON>,
} from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { ReportMissingSortTypes } from 'shared/enums'

class DateFilter {
	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiProperty({
		example: `${new Date().toISOString().split('T')[0]}T00:00:00.000Z`,
		required: false,
	})
	startDate!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiProperty({
		example: `${new Date().toISOString().split('T')[0]}T23:59:59.999Z`,
		required: false,
	})
	endDate!: string
}
export class GetReportsDto extends PaginationDto {
	@IsOptional()
	@IsString()
	@ApiPropertyOptional({
		example: 'and',
		required: false,
	})
	searchParam!: string

	@IsEnum(ReportMissingSortTypes)
	@IsNotEmpty()
	@ApiProperty({
		enum: ReportMissingSortTypes,
		enumName: 'SortTypes',
		required: false,
	})
	sortType!: (typeof ReportMissingSortTypes)[keyof typeof ReportMissingSortTypes]

	@IsOptional()
	@Transform(({ value }) => {
		if (value === '') {
			throw new Error('stores cannot be an empty string')
		}
		return value.split(',').map((val: string) => Number.parseInt(val, 10))
	})
	@IsArray()
	@ApiPropertyOptional({
		example: '1,2,3',
		type: String,
		required: false,
	})
	stores!: number[]

	@IsOptional()
	@Transform(({ value }) => value.split(','))
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'not-solved,solved,rejected,forwarded',
		type: String,
		required: false,
	})
	status!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiProperty({ example: '2021-10-10', required: false })
	startDate!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiProperty({ example: '2021-10-10', required: false })
	endDate!: string
}

// export type MissingCashbacks = {
// 	missingCashbacks: MissingCashback[];
// 	pagination:       Pagination;
// }

// export type MissingCashback = {
// 	uid:         number;
// 	storeLogo:   string;
// 	clickedTime: Date;
// 	amount:      number;
// 	title:       string;
// 	status:      Status;
// 	createdAt:   Date;
// }

// export type Status = "pending" | "confirmed" | "cancelled";

// export type Pagination = {
// 	totalCount: number;
// 	page:       number;
// 	limit:      number;
// }
