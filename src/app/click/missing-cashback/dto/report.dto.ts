import { ApiProperty } from '@nestjs/swagger'
import { ClickType } from 'app/click/dto/click-response.dto'
import { Transform } from 'class-transformer'
import { IsNotEmpty } from 'class-validator'
import destr from 'destr'
import { Types } from 'mongoose'
import { PaginationResponseType } from 'shared/dto'

export class ReportMissingCashbackType {
	@ApiProperty({
		type: Types.ObjectId,
		example: '65eef72b4d2e4417e86ba45f',
		required: true,
	})
	click!: string

	@ApiProperty({
		type: String,
		enum: ['new', 'old'],
		required: true,
	})
	userType!: 'new' | 'old'

	@ApiProperty({ type: Boolean, required: true })
	coupon!: boolean

	@ApiProperty({ type: String, required: true })
	orderId!: string

	@ApiProperty({ type: String, required: true })
	message!: string

	@ApiProperty({ type: Number, required: true })
	paidAmount!: number

	@ApiProperty({ type: String, enum: ['web', 'mobile'], required: true })
	platform!: 'web' | 'mobile'
}

export class ReportMissingCashbackDto {
	@ApiProperty({
		type: 'string',
		format: 'binary',
		required: true,
		description:
			'Invoice support only PNG, JPG, JPEG, WebP and PDF. File format with max size of 2MB',
	})
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	invoice?: any

	@ApiProperty({ type: ReportMissingCashbackType, required: true })
	@Transform(data => (data ? destr(data.value) : {}))
	@IsNotEmpty()
	reportData: ReportMissingCashbackType
}
export class ClicksResponse {
	@ApiProperty({ type: [ClickType] })
	clicks: ClickType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
