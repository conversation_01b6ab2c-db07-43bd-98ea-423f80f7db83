import { HttpException, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import mongoose from 'mongoose'
import { Model, Types } from 'mongoose'
import { uploadFileToAwsBucket } from 'shared/decorators'
import { MissedDealsDocument, ReqUser } from 'shared/entities'
import {
	MissingCashback,
	MissingCashbackDocument,
} from 'shared/entities/missing-cashback.entity'
import { UserTypes } from 'shared/enums'
import { ClickService } from '../click.service'
import { GetReportsDto } from './dto/get-reports'
import { ReportMissingCashbackType } from './dto/report.dto'

@Injectable()
export class MissingCashbackService {
	constructor(
		@InjectModel(MissingCashback.name)
		private missingCashback: Model<MissingCashbackDocument>,
		private clickService: ClickService,
	) {}

	async reportMissingCashback(
		dto: ReportMissingCashbackType,
		user: ReqUser,
		imageFile: Express.Multer.File,
	): Promise<string> {
		if (!imageFile) {
			throw new HttpException('Invoice is required', 400)
		}
		const invoice = await uploadFileToAwsBucket(imageFile)
		if (!invoice) {
			throw new HttpException('Failed to upload invoice', 500)
		}

		const click = await this.clickService.getClickById(dto.click)

		if (!click) {
			throw new HttpException('Invalid click', 400)
		}

		const userId = new mongoose.Types.ObjectId(user.id)

		const { click: clickIdString, ...dtoWithoutClick } = dto

		const clickId = new mongoose.Types.ObjectId(clickIdString)

		const payload = {
			...dtoWithoutClick,
			user: userId,
			click: clickId,
			title: click.title,
			invoice,
			store: click.store,
			orderDate: click.createdAt,
			affiliation: click.affiliation,
		}
		const result = await this.missingCashback.create(payload)
		return result.complaintId
	}

	async listMissingCashback(userData: ReqUser, query: GetReportsDto) {
		// const user = new Types.ObjectId(userData.id)
		const user = userData.id
		const filters: Record<string, object> = {}
		filters.user = new Types.ObjectId(user)
		if (query.stores && query.stores.length > 0) {
			filters.store = {
				$in: query.stores.map(storeId => new Types.ObjectId(storeId)),
			}
		}
		if (query.searchParam) {
			filters.$text = { $search: query.searchParam }
		}
		if (query.status && query.status.length > 0) {
			filters.status = { $in: query.status }
		}
		if (query.startDate && query.endDate) {
			filters.createdAt = {
				$gte: new Date(query.startDate),
				$lte: new Date(query.endDate),
			}
		}

		const pageSize = query.pageSize || 10
		const page = query.page || 1

		const missingCashbacks = await this.missingCashback
			.find(filters)
			.populate('store click')
			.sort({
				createdAt: query.sortType === 'oldest' ? 1 : -1,
			})
			.skip((page - 1) * pageSize)
			.limit(pageSize)
			.exec()
		const list = missingCashbacks.map(missingCashback => {
			const { uid, store, status, paidAmount, complaintId, createdAt, click } =
				missingCashback

			return {
				uid,
				storeLogo: store?.logo?.secureUrl,
				storeBgColor: store?.bgColor,
				clickedTime: click?.createdAt,
				referenceId: complaintId,
				amount: paidAmount,
				title: click?.title,
				status,
				createdAt,
			}
		})
		const totalCount = await this.missingCashback.countDocuments(filters)

		return {
			missingCashbacks: list,
			pagination: {
				page: query.page,
				pageSize: list.length,
				total: totalCount,
			},
		}
	}
}
