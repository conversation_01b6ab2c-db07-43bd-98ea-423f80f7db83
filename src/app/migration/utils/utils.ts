function toCamelCase(str: string): string {
	return str.replace(/([-_][a-z])/gi, $1 => {
		return $1.toUpperCase().replace('-', '').replace('_', '')
	})
}

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export function convertKeysToCamelCase(obj: any): any {
	if (typeof obj !== 'object' || obj === null) {
		return obj // Return the value if it is not an object
	}

	if (Array.isArray(obj)) {
		return obj.map(item => convertKeysToCamelCase(item))
	}

	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	const newObj: { [key: string]: any } = {}
	for (const key in obj) {
		// biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
		if (obj.hasOwnProperty(key)) {
			const camelCaseKey = toCamelCase(key)
			newObj[camelCaseKey] = convertKeysToCamelCase(obj[key]) // Recursive call for nested objects
		}
	}
	return newObj
}

export type UserBalance = {
	readonly uid: string
	readonly totalConfirmed?: string
	readonly countConfirmed?: string
	readonly totalPending?: string
	readonly countPending?: string
	readonly PaidAmount?: string
	readonly totalRefCommission?: string
	readonly confirmedRefCommission?: string
	readonly pendingRefCommission?: string
	readonly paidCommission?: string
	readonly FlipConfirmedAmount?: string
	readonly countFlipConfirmed?: string
	readonly FlipPendingAmount?: string
	readonly countFlipPending?: string
	readonly withdrawable?: number
	readonly totalEarned?: number
}
