export class ProgressBar {
	totalItems: number
	processedItems: number
	name: string

	constructor(name: string, totalItems: number) {
		this.totalItems = totalItems
		this.processedItems = 0
		this.name = name
	}

	update(data = '') {
		this.processedItems++
		const percentage = ((this.processedItems / this.totalItems) * 100).toFixed(
			2,
		)
		// process.stdout.clearLine(0) // Clear the current text; 0 is for clearing the entire line
		// process.stdout.cursorTo(0) // Move cursor to start of line
		// process.stdout.write(
		// 	`Progress: ${this.name} - ${data} [${this.processedItems} / ${this.totalItems} - ${percentage}%]`,
		// )
		console.log(
			`Progress: ${this.name} - ${data} [${this.processedItems} / ${this.totalItems} - ${percentage}%]`,
		)
	}

	complete() {
		// process.stdout.write(`\n Migration ${this.name} Completed \n`)
		console.log(`\n Migration ${this.name} Completed \n`)
	}
}
