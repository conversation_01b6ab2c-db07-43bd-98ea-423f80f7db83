import { MongoClient } from 'mongodb'

export async function cloneDatabase(
	sourceUrl: string,
	targetUrl: string,
	sourceDbName: string,
	targetDbName: string,
) {
	let sourceClient: MongoClient | null = null
	let targetClient: MongoClient | null = null

	try {
		// Connect to source and target MongoDB instances
		sourceClient = await MongoClient.connect(sourceUrl)
		targetClient = await MongoClient.connect(targetUrl)

		const sourceDb = sourceClient.db(sourceDbName)
		const targetDb = targetClient.db(targetDbName)

		// Drop the target database before cloning
		console.log(`Dropping target database: ${targetDbName}`)
		await targetDb.dropDatabase()

		// Get all collections in the source database
		const collections = await sourceDb.listCollections().toArray()

		for (const collection of collections) {
			const collectionName = collection.name
			console.log(`Cloning collection: ${collectionName}`)

			// Read all documents from the source collection
			const sourceCollection = sourceDb.collection(collectionName)
			const documents = await sourceCollection.find({}).toArray()

			// Insert documents into the target collection
			if (documents.length > 0) {
				const targetCollection = targetDb.collection(collectionName)
				await targetCollection.insertMany(documents)
			}
		}

		console.log(
			`Database ${sourceDbName} cloned successfully to ${targetDbName}!`,
		)
	} catch (error) {
		console.error('An error occurred during the cloning process:', error)
	} finally {
		// Close database connections
		if (sourceClient) {
			await sourceClient.close()
		}
		if (targetClient) {
			await targetClient.close()
		}
	}
}
