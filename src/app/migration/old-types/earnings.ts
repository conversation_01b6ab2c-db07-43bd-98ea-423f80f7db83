export type OldUserEarnings = {
	readonly earningId: number
	readonly uid: number
	readonly storeId: number
	readonly amount: number
	readonly amount_got: number
	readonly amount_promised: string
	readonly sale_amount: number
	readonly order_count: number
	readonly partner_id: number
	readonly notes: string
	readonly advertiser_info: string
	readonly other_info: string
	readonly remarks: string
	readonly status: Status
	readonly order_unique_id: string
	readonly checked_to_cancel: number
	readonly checked_to_confirm: number
	readonly addedDate: Date
	readonly confirmDate: Date
	readonly date_Confirmed_cancelled: Date | string
	readonly auto_updated: number
}

export type Status = 'Confirmed' | 'Cancelled' | 'Processing' | 'Pending'
