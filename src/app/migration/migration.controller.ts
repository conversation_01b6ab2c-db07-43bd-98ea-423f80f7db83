import { Controller, Delete, Post } from '@nestjs/common'
import { ApiBasicAuth, ApiSecurity, ApiTags } from '@nestjs/swagger'
import { MigrationService } from './migration.service'

@ApiTags('Migration')
@Controller('migration')
export class MigrationController {
	constructor(private readonly migrationService: MigrationService) {}

	@Delete()
	async migrate() {
		await this.migrationService.migrate()
	}

	@Post('clone')
	async cloneDatabase() {
		await this.migrationService.cloneDatabase()
	}
}
