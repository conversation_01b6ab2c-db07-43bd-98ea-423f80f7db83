import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { CashbackRateType } from 'app/store/types/get-all-stores.types'
import { Model, Types } from 'mongoose'
import { StoreCategory, StoreCategoryDocument } from 'shared/entities'
import { buildStoreCategoryByStore } from 'shared/helpers/store-category.helper'

@Injectable()
export class StoreCategoryService {
	constructor(
		@InjectModel(StoreCategory.name)
		private readonly storeCategory: Model<StoreCategoryDocument>,
	) {}

	async getPopulatedStoreCategoryByUid(uid: number) {
		return this.storeCategory
			.findOne({ uid })
			.populate('affiliation store')
			.exec()
	}

	async getCashbackRatesByStoreId(storeId: string) {
		const aggregationPipeline = buildStoreCategoryByStore(storeId)
		const results = await this.storeCategory
			.aggregate(aggregationPipeline)
			.exec()

		const cashbackRates = results
			.map(
				(item): CashbackRateType => ({
					name: item.name,
					uid: item.uid,
					description: item.description,
					oldUserRate: item?.oldUserOfferAmount ?? item?.oldUserOfferPercent,
					newUserRate: item?.newUserOfferAmount ?? item?.newUserOfferPercent,
					type:
						item?.oldUserOfferAmount || item?.newUserOfferAmount
							? 'amount'
							: ('percent' as 'amount' | 'percent'),
				}),
			)
			.sort((a, b) => b.newUserRate - a.newUserRate)

		return { cashbackRates }
	}
}
