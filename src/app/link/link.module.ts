import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { Earning, EarningSchema } from '@shared/entities/earning.entity'
import {
	Click,
	ClickSchema,
	Link,
	LinkSchema,
	Store,
	StoreSchema,
	User,
	UserSchema,
} from 'shared/entities'
import { LinkController } from './link.controller'
import { LinkService } from './link.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: Link.name,
				schema: LinkSchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: User.name,
				schema: UserSchema,
			},
			{
				name: Click.name,
				schema: ClickSchema,
			},
			{
				name: Earning.name,
				schema: EarningSchema,
			},
		]),
	],
	controllers: [LinkController],
	providers: [LinkService],
	exports: [LinkService],
})
export class LinkModule {}
