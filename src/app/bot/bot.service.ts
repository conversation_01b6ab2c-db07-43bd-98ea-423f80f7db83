import { env } from '@config'
import { run } from '@grammyjs/runner'
import { Injectable, OnModuleInit } from '@nestjs/common'
import { Bot } from 'grammy'

@Injectable()
export class BotService implements OnModuleInit {
	private bot: Bot
	private chatId = -1002559017139
	private newUserAlertThreadId = 1262

	onModuleInit() {
		this.bot = new Bot(env.TELEGRAM.botToken)
		run(this.bot)
	}

	async sendNewUserAlert(message: string) {
		await this.bot.api.sendMessage(this.chatId, message, {
			// biome-ignore lint/style/useNamingConvention: <explanation>
			message_thread_id: this.newUserAlertThreadId,
			// biome-ignore lint/style/useNamingConvention: <explanation>
			parse_mode: 'HTML',
		})
	}

	getBot() {
		return this.bot
	}
}
