import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { Click, ClickSchema, User, UserSchema } from '@shared/entities'
import { BotStatsService } from './bot-status.service'
import { BotService } from './bot.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: Click.name,
				schema: ClickSchema,
			},
			{
				name: User.name,
				schema: UserSchema,
			},
		]),
	],
	providers: [BotService, BotStatsService],
	exports: [BotService],
})
export class BotModule {}
