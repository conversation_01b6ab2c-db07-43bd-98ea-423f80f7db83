import { Module, forwardRef } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { SavedItemModule } from 'app/saved-item/saved-item.module'
import { StoreModule } from 'app/store/store.module'
import { UserModule } from 'app/user/user.module'
import {
	Offer,
	OfferSchema,
	Store,
	StoreSchema,
	SubCategory,
	SubCategorySchema,
} from 'shared/entities'
import {
	OngoingSaleOffers,
	OngoingSaleOffersSchema,
} from 'shared/entities/ongoing-sale-offers.entity'
import { OfferController } from './offer.controller'
import { OfferService } from './offer.service'

//NOTE - only the Offer based entity is being used in the Offers module.
// The other entities are needed to be imported in the respective modules.
@Module({
	imports: [
		SavedItemModule,
		UserModule,
		MongooseModule.forFeature([
			{
				name: Offer.name,
				schema: OfferSchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: OngoingSaleOffers.name,
				schema: OngoingSaleOffersSchema,
			},
			{
				name: SubCategory.name,
				schema: SubCategorySchema,
			},
		]),
	],
	controllers: [OfferController],
	providers: [OfferService],
	exports: [OfferService],
})
export class OfferModule {}
