import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { <PERSON>ron } from '@nestjs/schedule'
import { ContextOfferDealsType } from 'app/context/types/offers.types'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { UserService } from 'app/user/user.service'
import mongoose, { Model, mongo } from 'mongoose'
import {
	Categories,
	Offer,
	OfferDocument,
	ReqUser,
	SavedItemDocument,
	Store,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
	UserDocument,
} from 'shared/entities'
import {
	OngoingSaleOffers,
	OngoingSaleOffersDocument,
} from 'shared/entities/ongoing-sale-offers.entity'
import { OfferTypes, UserTypes } from 'shared/enums'
import {
	buildOfferAggregateQuery,
	buildOngoingOfferAggregateQuery,
	buildSingleOfferAndSimilarOffers,
	generateOfferCaption,
} from 'shared/helpers/offer.helper'
import {
	convertLocalToUTC,
	convertLocalToUTCDate,
	getFinalDateToShow,
} from 'shared/helpers/time.helper'
import { SavedEnum } from 'shared/types'
import {
	CouponsAndDealsDto,
	OngoingSalesDto,
	RemoveOfferDto,
	SaveOfferDto,
} from './dto/offer.dto'
import {
	DealAndCouponsResponse,
	OfferCouponsType,
	OngoingOfferType,
	OngoingOffersResponse,
} from './types/offer.types'

@Injectable()
export class OfferService {
	constructor(
		@InjectModel(Offer.name)
		private offers: Model<OfferDocument>,

		@InjectModel(Store.name)
		private store: Model<StoreDocument>,

		@InjectModel(OngoingSaleOffers.name)
		private ongoingSales: Model<OngoingSaleOffersDocument>,

		@InjectModel(SubCategory.name)
		private readonly subCategories: Model<SubCategoryDocument>,

		private readonly userService: UserService,

		private savedItemService: SavedItemService,
	) {}
	async getAllOffers(
		queryParams: CouponsAndDealsDto,
		userSession: ReqUser,
	): Promise<DealAndCouponsResponse> {
		const skipAvailabilityCheck = false

		if (!skipAvailabilityCheck) {
			await this.storeOfferAvailabilityCheck(queryParams)
		}

		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.Offer],
					user._id,
				)
			: []

		const subCategoryIds =
			queryParams?.subCategories?.length && queryParams.subCategories.length > 0
				? (queryParams?.subCategories?.split(',').map(Number) ?? [])
				: []

		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories
			.find({ uid: { $in: subCategoryIds } })
			.exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = await buildOfferAggregateQuery(
			queryParams,
			subCategoryObjectIds,
		)
		const filteredOffers = await this.offers
			.aggregate(aggregationPipeline)
			.exec()

		const offersWithSavedState = filteredOffers[0].documents.map(
			(offer: OngoingOfferType) => {
				const isSaved = usersSavedOffers.some(
					savedOffer => savedOffer.itemUid === offer.uid,
				)

				return {
					...offer,
					saved: isSaved,
					endDate:
						offer?.repeatBy === 'true'
							? getFinalDateToShow(offer.endDate as unknown as string)
							: (offer?.endDate as unknown as string),
				}
			},
		)
		return {
			offers: offersWithSavedState,
			pagination: {
				page: queryParams.page,
				pageSize: filteredOffers[0].documents?.length,
				total: filteredOffers[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getOngoingOffers(
		queryParams: OngoingSalesDto,
		userSession?: ReqUser,
	): Promise<OngoingOffersResponse> {
		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.Offer],
					user._id,
				)
			: []

		const subCategoryIds =
			queryParams?.subCategories?.length && queryParams.subCategories.length > 0
				? (queryParams?.subCategories?.split(',').map(Number) ?? [])
				: []

		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories
			.find({ uid: { $in: subCategoryIds } })
			.exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = buildOngoingOfferAggregateQuery(
			queryParams,
			subCategoryObjectIds,
		)

		const filteredOffers = await this.ongoingSales
			.aggregate(aggregationPipeline)
			.exec()

		const offersWithSavedState = filteredOffers[0].documents
			.filter(
				(offer: OngoingOfferType, index: number, self: OngoingOfferType[]) =>
					index ===
					self.findIndex((o: OngoingOfferType) => o.uid === offer.uid),
			)
			.map((offer: OngoingOfferType) => ({
				...offer,
				saved: usersSavedOffers.some(
					savedOffer => savedOffer.itemUid === offer.uid,
				),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		return {
			offers: offersWithSavedState,
			pagination: {
				page: queryParams.page,
				pageSize: filteredOffers[0].documents?.length,
				total: filteredOffers[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getPopulatedOfferByUid(uid: number) {
		return await this.offers
			.findOne({ uid })
			.populate('store affiliation')
			.exec()
	}

	async getOfferByUid(uid: number) {
		return await this.offers.findOne({ uid }).exec()
	}

	async saveOffer(saveParams: SaveOfferDto, userSession: ReqUser) {
		const user = (await this.userService.getUserByEmail(
			userSession.email,
		)) as UserDocument

		if (!user) {
			throw new NotFoundException('User not found')
		}

		const offer = await this.getOfferByUid(saveParams.itemUid)

		if (!offer) {
			throw new NotFoundException('Offer not found')
		}

		await this.savedItemService.saveItem(user, SavedEnum.Offer, offer)
		return {
			message: 'Offer saved successfully',
		}
	}

	async removeSavedOffer(userSession: ReqUser, params: RemoveOfferDto) {
		const user = await this.userService.getUserByEmail(userSession.email)

		if (!user) {
			throw new NotFoundException('User not found')
		}

		// Remove the saved item
		await this.savedItemService.removeItem(
			user,
			SavedEnum.Offer,
			params.itemUid,
		)

		return {
			message: 'Offer removed successfully.',
		}
	}
	async getOfferById(id: string) {
		const offerDetails = (await this.offers
			.findById(id)
			.populate(
				'store',
				'name affiliation minimumAmount maximumAmount missingAccepted confirmationTime logo bgColor ',
			)
			.populate('storeCategory', 'gettingNewUserRate gettingOldUserRate')
			.exec()) as Offer

		if (!offerDetails) {
			throw new NotFoundException('Offer  not found')
		}

		const result = {
			uid: offerDetails.uid,
			productImage: offerDetails.productImage.secureUrl,
			offerTitle: offerDetails.title,
			offerCaption: offerDetails.offer,
			salePrice: offerDetails.itemPrice,
			discount: offerDetails.discount,
			offerPercentage: offerDetails.offerPercent,
			storeLogoUrl: offerDetails.store.logo.secureUrl,
			description: offerDetails.description,
			terms: offerDetails.terms,
			storeName: offerDetails.store.name,
			minimumAmount: offerDetails.store.minimumAmount,
			trackingTime: offerDetails.store.trackingTime,
			missingAccepted: offerDetails.store.missingAccepted,
			confirmationTime: offerDetails.store.confirmationTime,
			endDate: offerDetails.dateExpiry as Date,
			newUserRate: offerDetails?.storeCategory?.gettingNewUserRate,
			oldUserRate: offerDetails?.storeCategory?.gettingOldUserRate,
		}

		return { offer: result }
	}

	async getOfferAndSimilarOffers(uid: number, userSession?: ReqUser) {
		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.Offer],
					user._id,
				)
			: []
		const offerDetails = (await this.offers
			.findOne({ uid })
			.populate(
				'store',
				'name minimumAmount missingAccepted repeatBy confirmationTime logo trackingTime offerWarning bgColor isAutoGenerated hideCbTag',
			)
			.populate({
				path: 'storeCategory',
				select: 'gettingNewUserRate gettingOldUserRate',
				options: { strictPopulate: false }, // Prevents errors if storeCategory is missing
			})
			.exec()) as OfferDocument

		if (!offerDetails) {
			throw new NotFoundException('Offer  not found')
		}
		const aggregatePipeline = buildSingleOfferAndSimilarOffers(
			uid,
			offerDetails.store._id,
		)
		const similarOffers = await this.offers.aggregate(aggregatePipeline).exec()

		const similarOffersWithSavedState = similarOffers
			.filter(
				(offer: OfferCouponsType, index: number, self: OfferCouponsType[]) =>
					index ===
					self.findIndex((o: OfferCouponsType) => o.uid === offer.uid),
			)
			.map((offer: OfferCouponsType) => ({
				...offer,
				saved: usersSavedOffers.some(
					savedOffer => savedOffer.itemUid === offer.uid,
				),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		const result = {
			uid: offerDetails.uid, //
			productImage: offerDetails.productImage.secureUrl, //
			offerTitle: offerDetails.title, //
			offerCaption: generateOfferCaption({
				offerType: offerDetails?.offerType,
				offerAmount: offerDetails?.offerAmount,
				offerPercent: offerDetails?.offerPercent,
				cashbackType: offerDetails?.store?.cashbackType,
			}),
			offerAmount: offerDetails.offerAmount, //
			itemPrice: offerDetails.itemPrice,
			offerPercent: offerDetails.offerPercent,
			storeLogoUrl: offerDetails.store.logo.secureUrl, //
			storeBgColor: offerDetails?.store?.bgColor,
			offerDescription: offerDetails.description, //
			termsAndConditions: offerDetails.terms, //
			storeName: offerDetails?.store?.name, //
			storeId: offerDetails?.store?._id,
			minimumAmount: offerDetails?.store?.minimumAmount, //
			trackingTime: offerDetails?.store?.trackingTime, //
			missingAccepted: offerDetails?.store?.missingAccepted, //
			confirmationTime: offerDetails?.store?.confirmationTime, //
			endDate:
				offerDetails?.repeatBy === 'true'
					? getFinalDateToShow(offerDetails.dateExpiry as unknown as string)
					: (offerDetails?.dateExpiry as unknown as string),
			newUserRate: offerDetails?.storeCategory?.gettingNewUserRate, //
			oldUserRate: offerDetails?.storeCategory?.gettingOldUserRate, //
			offerWarning: offerDetails?.store?.offerWarning, //
			keySpecs: offerDetails?.keySpecs, //
			importantUpdate: offerDetails?.importantUpdate,
			offerType: offerDetails?.offerType,
			couponCode: offerDetails?.couponCode,
			isExpired:
				offerDetails?.active === false ||
				new Date(offerDetails?.dateExpiry as Date) <
					new Date(convertLocalToUTC()),
			isAutoGenerated: offerDetails?.isAutoGenerated,
			hideCbTag: offerDetails?.hideCbTag,
		}

		return { offer: result, similarOffers: similarOffersWithSavedState }
	}

	async getOffersByTitleAndSimilarOffers(title: string, userSession?: ReqUser) {
		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.Offer],
					user._id,
				)
			: []

		// Extract the first part of the title before the hyphen and convert to lowercase
		const sanitizedTitle = (title?.split('-')[0] ?? '').toLowerCase()

		// Find the store by name
		const store = await this.store
			.findOne({
				name: { $regex: new RegExp(sanitizedTitle, 'i') },
			})
			.exec()

		if (!store) {
			// Fetch trending offers with a limit of 15
			const trendingDeals: Offer[] = await this.offers
				.find({
					trending: true,
					dateExpiry: { $gt: convertLocalToUTC() },
					active: true,
				}) // Filter expired offers
				.limit(15) // Limit to 15 results
				.populate('store')
				.sort({ trendingPriority: -1, dateExpiry: -1 })
				.exec()

			const formattedTrendingDeal: ContextOfferDealsType[] = trendingDeals.map(
				offer => {
					// Check if the offer is saved by the user based on uid
					const saved = usersSavedOffers.some(
						savedItem => savedItem.itemUid === offer.uid,
					)
					return {
						uid: offer?.uid,
						offerCaption: offer?.offer,
						productImage: offer?.productImage?.secureUrl,
						storeLogoUrl: offer?.store?.logo?.secureUrl,
						storeBgColor: offer?.store?.bgColor,
						storeName: offer?.store?.name,
						offerPercentage: offer?.offerPercent,
						repeatedBy: offer?.repeatBy,
						endDate:
							offer?.repeatBy === 'true'
								? getFinalDateToShow(offer.dateExpiry as unknown as string)
								: (offer?.dateExpiry as unknown as string),
						offerTitle: offer?.title,
						...(offer?.offer && offer?.offer.trim() !== ''
							? { offerCaption: offer?.offer }
							: {}),
						salePrice: offer.itemPrice ? offer?.itemPrice : 0,
						...(offer.migrated ? { offerUrl: offer?.url } : {}),
						saved: saved,
					}
				},
			)

			return { similarOffers: formattedTrendingDeal }
		}

		const aggregatePipeline = buildSingleOfferAndSimilarOffers(0, store._id)
		const similarOffers = await this.offers.aggregate(aggregatePipeline).exec()

		const similarOffersWithSavedState = similarOffers
			.filter(
				(offer: OfferCouponsType, index: number, self: OfferCouponsType[]) =>
					index ===
					self.findIndex((o: OfferCouponsType) => o.uid === offer.uid),
			)
			.map((offer: OfferCouponsType) => ({
				...offer,
				saved: usersSavedOffers.some(
					savedOffer => savedOffer.itemUid === offer.uid,
				),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		return { similarOffers: similarOffersWithSavedState }
	}

	// @Cron('*/20 * * * * *')

	@Cron('0 0 * * *') // Runs every day at midnight
	async autoGenerateOffersCron() {
		const stores = await this.store.find({ active: true })
		let count = 0
		const newStoreUid = []

		console.log(
			`[CRON] Starting autoGenerateOffersCron at ${new Date().toISOString()}`,
		)

		for (const storeItem of stores) {
			const offer = await this.offers.findOne({
				store: new mongoose.Types.ObjectId(storeItem?.id),
				active: true,
				dateExpiry: { $gt: convertLocalToUTCDate() },
			})

			if (!offer) {
				console.log(
					`[CRON] No active offer found for store: ${storeItem.name} (ID: ${storeItem.id})`,
				)
				count += 1
				await this.checkAndUpdateOffersForStore(storeItem.id)
				// const newOffer = await this.generateOfferFromStoreId(storeItem.id)
				// if (newOffer) {
				// 	newStoreUid.push(newOffer.uid)
				// 	console.log(
				// 		`[CRON] New offer created for store: ${storeItem.name} (ID: ${storeItem.id}) | Offer UID: ${newOffer.uid}`,
				// 	)
				// }
			}
		}

		console.log(`[CRON] Total new offers added: ${count}`)
	}

	async storeOfferAvailabilityCheck(queryParams: CouponsAndDealsDto) {
		console.log(
			`[CHECK] Starting storeOfferAvailabilityCheck with params: ${JSON.stringify(
				queryParams,
			)}`,
		)

		if (queryParams.storeId) {
			console.log(
				`[CHECK] Checking offers for store ID: ${queryParams.storeId}`,
			)
			await this.checkAndUpdateOffersForStore(queryParams.storeId)
		} else if (queryParams.subCategories) {
			console.log(
				`[CHECK] Checking offers for subcategories: ${queryParams.subCategories}`,
			)
			const storesList = await this.getStoresBySubCategories(
				queryParams.subCategories,
			)

			for (const store of storesList) {
				console.log(
					`[CHECK] Checking offers for store: ${store.name} (ID: ${store.id})`,
				)
				await this.checkAndUpdateOffersForStore(store.id)
			}
		}
	}

	private async checkAndUpdateOffersForStore(storeId: string) {
		console.log(
			`[UPDATE] Checking and updating offers for store ID: ${storeId}`,
		)

		const storeObjectId = new mongoose.Types.ObjectId(storeId)
		const baseQuery = {
			store: storeObjectId,
			active: true,
			dateExpiry: { $gt: convertLocalToUTCDate() },
		}

		const totalOfferCount = await this.offers.countDocuments(baseQuery)
		console.log(
			`[UPDATE] Total active offers for store ID ${storeId}: ${totalOfferCount}`,
		)

		if (totalOfferCount === 0) {
			console.log(`[UPDATE] No active offers found for store ID: ${storeId}`)

			console.log(
				`[UPDATE] Check if there is already generated default offer for store ID: ${storeId}`,
			)

			const inactiveAutomatedOfferCount = await this.offers.countDocuments({
				...baseQuery,
				isAutoGenerated: true,
				active: false,
			})

			console.log(
				`[UPDATE] Inactive automated offers for store ID ${storeId}: ${inactiveAutomatedOfferCount}`,
			)

			if (inactiveAutomatedOfferCount === 0) {
				console.log(
					`[UPDATE] No inactive automated offers found for store ID: ${storeId}`,
				)
				await this.generateOfferFromStoreId(storeId)

				return
			}
			console.log(
				`[UPDATE] Activating automated offers for store ID: ${storeId}`,
			)
			await this.offers.updateMany(
				{ ...baseQuery, isAutoGenerated: true, active: false },
				{ $set: { active: true } },
			)

			return
		}

		const automatedOfferCount = await this.offers.countDocuments({
			...baseQuery,
			isAutoGenerated: true,
		})

		console.log(
			`[UPDATE] Automated offers for store ID ${storeId}: ${automatedOfferCount}`,
		)

		if (totalOfferCount > 1 && automatedOfferCount >= 1) {
			console.log(
				`[UPDATE] Deactivating automated offers for store ID: ${storeId}`,
			)
			await this.offers.updateMany(
				{ ...baseQuery, isAutoGenerated: true },
				{ $set: { active: false } },
			)
		}
	}

	private async getStoresBySubCategories(
		subCategoriesParam: string,
	): Promise<any[]> {
		console.log(
			`[FETCH] Fetching stores for subcategories: ${subCategoriesParam}`,
		)

		const subCategoryIds = subCategoriesParam
			.split(',')
			.map(Number)
			.filter(Boolean)

		if (!subCategoryIds.length) {
			console.log('[FETCH] No valid subcategory IDs found.')
			return []
		}

		const subCategories = await this.subCategories
			.find({ uid: { $in: subCategoryIds } })
			.exec()

		const objectIdSubcategoryIds = subCategories.map(
			sc => new mongoose.Types.ObjectId(sc._id),
		)

		console.log(
			`[FETCH] Found subcategories: ${subCategories
				.map(sc => sc.name)
				.join(', ')}`,
		)

		return this.store
			.find({
				'categories.subCategories': {
					$elemMatch: { $in: objectIdSubcategoryIds },
				},
			})
			.populate('categories.category')
			.populate('categories.subCategories')
			.exec()
	}

	async generateOfferFromStoreId(storeId: string) {
		console.log(`[GENERATE] Generating offer for store ID: ${storeId} `)

		const currentStore: Store = (await this.store.findOne({
			_id: storeId,
			active: true,
		})) as Store

		if (!currentStore) {
			console.log(`[GENERATE] Store not found or inactive for ID: ${storeId}`)
			return
		}

		const updatedStoreOffer = await this.updateStoreOffer(
			currentStore.storeOffer,
		)

		// Update the title of the automated offer
		const updatedTitle = `Get Up to ${updatedStoreOffer} from us on top of all other offers from ${currentStore.name}`

		const newOfferParams = {
			isAutoGenerated: true,
			hideCbTag: true,
			store: new mongoose.Types.ObjectId(storeId),
			offerAmount: currentStore?.cashbackAmount
				? currentStore?.cashbackAmount
				: '',
			offerPercent: currentStore?.cashbackPercent
				? currentStore?.cashbackPercent
				: '',
			offerType: currentStore.offerType,
			affiliation: currentStore.affiliation,
			title: updatedTitle,
			discount: '',
			offer: currentStore?.storeOffer,
			itemPrice: '',
			link: currentStore?.affiliateLink ?? '',
			isCoupon: false,
			couponCode: '',
			description: `<ul>
  <li>ICB cashback can be redeemed as real cash, 1 ICB cashback = INR 1</li>
  <li>ICB cashback is on top of all other offers available at ${currentStore?.name}</li>
  <li>Check Terms & Conditions for more details. <a href="https://indiancashback.com/terms-and-conditions/" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">Click here</a> </li>
  <li>If you have any doubts you can connect with us <a href="https://tawk.to/indiancashback" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">here</a>.</li>
</ul>`,
			keySpecs: '',
			dateStart: convertLocalToUTCDate(),
			dateExpiry: new Date(
				new Date().setFullYear(new Date().getFullYear() + 1),
			),
			categories: currentStore?.categories ?? [],
			stockEnds: false,
			repeatBy: 'true',
			terms: `<p>&nbsp;</p><blockquote><p><strong>Terms and conditions to get this offer:</strong></p><ul><li>Offer valid only till stock ends.&nbsp;</li><li>You will get cashback only if you purchased from the automatically opened merchant’s website by indiancashback.com&nbsp;</li><li>Restrictions may apply in some cases.</li><li>Cashback is not applicable if the sale is cancelled or if the goods are returned.</li><li>Cashback is often paid excluding VAT, delivery and other administrative charges.</li><li>The usage of coupon will reflect in cashback amount at the time of pending cashback amount raises to approved state.</li><li>You can raise a missing cashback ticket for your transaction within 30 days of the purchase date. Any missing cashback ticket after 30 days will not be accepted by the merchant site.</li><li>Cashback may not paid on purchases made using store credits &amp; Gift vouchers.&nbsp;</li><li>Using a Coupon found outside&nbsp;<a href="https://indiancashback.com/">indiancashback com</a>&nbsp; may void your Cashback.&nbsp;</li><li>Any fraudulent behavior may result cancellation of your Cashback and you may get blacklisted by the merchant.&nbsp;</li><li>Cashback is not applicable if the sale is made using store credits.<br>&nbsp;</li></ul><p><br>&nbsp;</p></blockquote>`,
			productImage: currentStore?.logo,
			importantUpdate: '',
			userType: 'both',
			createdBy: new mongoose.Types.ObjectId('66f5900c0cd63ffff0ce2cd6'), // auto bot user id
		}

		const newOffer = await new this.offers(newOfferParams).save()

		console.log(
			`[GENERATE] New offer created for store ID: ${storeId} | Offer ID: ${newOffer._id}`,
		)

		return newOffer
	}

	async updateAutomatedOffers() {
		const stores = await this.store.find({
			active: true,
			//  _id: new mongoose.Types.ObjectId('66f206ec28628113b4856f42')
		})
		// let count = 0;
		const updatedOffers = []

		console.log(
			`[CRON] Starting autoGenerateOffersCron at ${new Date().toISOString()}`,
		)

		for (const storeItem of stores) {
			// Check for active automated offers
			const automatedOffers = await this.offers.find({
				store: new mongoose.Types.ObjectId(storeItem?.id),
				isAutoGenerated: true,
			})

			if (automatedOffers.length > 0) {
				for (const offer of automatedOffers) {
					// const updatedStoreOffer = await this.updateStoreOffer(
					// 	storeItem.storeOffer,
					// )

					// Update the title of the automated offer
					// const updatedTitle = `Get ${updatedStoreOffer} from us on top of all other offers from ${storeItem.name}`

					const updatedDescription = `<ul>
  <li>ICB cashback can be redeemed as real cash, 1 ICB cashback = INR 1</li>
  <li>ICB cashback is on top of all other offers available at ${storeItem?.name}</li>
  <li>Check Terms & Conditions for more details. <a href="https://indiancashback.com/terms-and-conditions/" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">Click here</a> </li>
  <li>If you have any doubts you can connect with us
    <a href="https://tawk.to/indiancashback" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">here.</a>
  </li>
</ul>`
					// const updatedTitle = `Grab ${storeItem.storeOffer} from ${storeItem.name}`;
					await this.offers.updateOne(
						{ _id: offer._id },
						{ $set: { hideCbTag: true, description: updatedDescription } },
					)
					console.log(
						`[CRON] Updated offer title for store: ${storeItem.name} (ID: ${storeItem.id}) | Offer ID: ${offer._id}`,
					)
					updatedOffers.push(offer._id)
				}
			}
		}

		console.log(`[CRON] Total offers updated: ${updatedOffers.length}`)
	}

	// Function to prepend "ICB" to "cashback" or "reward"
	async updateStoreOffer(storeOffer: string): Promise<string> {
		// Use a regular expression to match "cashback" or "reward" (case-insensitive)
		return storeOffer.replace(/(cashback|reward)/gi, 'ICB $1')
	}

	async logAutomatedOffers() {
		//populate store
		const automatedOffers = await this.offers
			.find({
				isAutoGenerated: true,
			})
			.populate('store')

		for (const offer of automatedOffers) {
			console.log(
				`[CRON] Automated offer: : from store ${offer.store.name} - ${offer._id}`,
			)
		}
	}
}

// {
//   totalExecutionTime: '158.83 ms',
//   availabilityCheckTime: 'Skipped',
//   mainLogicTime: '158.83 ms',
//   availabilityCheckPercentage: 0
// }

// {
// 	totalExecutionTime: '241.82 ms',
// 		availabilityCheckTime: '0.36 ms',
// 			mainLogicTime: '241.45 ms',
// 				availabilityCheckPercentage: '0.15%'
// }
