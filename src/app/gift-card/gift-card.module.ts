import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { ScheduleModule } from '@nestjs/schedule'
import { SavedItemModule } from 'app/saved-item/saved-item.module'
import { UserModule } from 'app/user/user.module'
import { Banner, BannerSchema, GiftCard, GiftCardSchema } from 'shared/entities'
import {
	GiftCardOrder,
	GiftCardOrderSchema,
} from 'shared/entities/gift-card-order.entity'
import { IcbGiftCard, IcbGiftCardSchema } from 'shared/entities/icb-gift-card'
import {
	PaymentSession,
	PaymentSessionSchema,
} from 'shared/entities/payment-session.entity'
import { RazorpayModule } from 'shared/modules'
import { GiftCardController } from './gift-card.controller'
import { GiftCardService } from './gift-card.service'

@Module({
	imports: [
		RazorpayModule,
		SavedItemModule,
		UserModule,
		ScheduleModule.forRoot(),
		MongooseModule.forFeature([
			{
				name: GiftCard.name,
				schema: GiftCardSchema,
			},
			{ name: Banner.name, schema: BannerSchema },
			{ name: IcbGiftCard.name, schema: IcbGiftCardSchema },
			{ name: PaymentSession.name, schema: PaymentSessionSchema },
			{ name: GiftCardOrder.name, schema: GiftCardOrderSchema },
		]),
	],

	controllers: [GiftCardController],
	providers: [GiftCardService],
	exports: [GiftCardService],
})
export class GiftCardModule {}
