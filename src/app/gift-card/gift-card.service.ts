import {
	Injectable,
	NotAcceptableException,
	NotFoundException,
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { SearchGiftCardResponse } from 'app/context/types/search.types'
import { RemoveOfferDto, SaveOfferDto } from 'app/offer/dto/offer.dto'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { OrderResponse } from 'app/saved-item/types/saved-item.types'
import { UserService } from 'app/user/user.service'
import { env } from 'config'
import { Model, Types } from 'mongoose'
import { murmurHash } from 'ohash'
import {
	Banner,
	BannerDocument,
	GiftCard,
	GiftCardDocument,
	ReqUser,
	SavedItemDocument,
	UserDocument,
} from 'shared/entities'
import {
	GiftCardOrder,
	GiftCardOrderDocument,
} from 'shared/entities/gift-card-order.entity'
import { IcbGiftCard, IcbGiftCardDocument } from 'shared/entities/icb-gift-card'
import {
	PaymentSession,
	PaymentSessionDocument,
} from 'shared/entities/payment-session.entity'
import { BannerTypes } from 'shared/enums'
import { sendGiftCardMail } from 'shared/functions/mails/brevo'
import {
	buildGiftCardRedeemHistorySortQuery,
	buildSortQuery,
} from 'shared/helpers/query-builder'
import { currentTime } from 'shared/helpers/time.helper'
import {
	BalancePaymentProcessor,
	PaymentProcessor,
	RazorpayPaymentProcessor,
} from 'shared/modules/payments'
import { RazorpayService } from 'shared/modules/razorpay/razorpay.service'
import { SavedEnum } from 'shared/types'
import { GetAllGiftCardsDto } from './dto/get-gift-card.dto'
import {
	GetRedeemGiftCardsDto,
	OrderGiftCardDto,
	PaymentVerifyDto,
	RedeemIcbGiftCardDto,
} from './dto/order-giftcard.dto'
import {
	buildGiftCardAggregateQuery,
	buildGiftCardRedeemHistoryAggregateQuery,
} from './gift-card.helper'
import { CreateIcbGiftCard } from './types/create-gift-card.types'
import {
	GetGiftCardListResponse,
	GetGiftCardResponse,
	GiftCardBannersResponse,
	GiftCardDetails,
} from './types/get-gift-card.types'

@Injectable()
export class GiftCardService {
	constructor(
		@InjectModel(GiftCard.name)
		private readonly giftCard: Model<GiftCardDocument>,
		@InjectModel(IcbGiftCard.name)
		private readonly icbGiftCard: Model<IcbGiftCardDocument>,
		@InjectModel(Banner.name)
		private readonly banners: Model<BannerDocument>,
		@InjectModel(PaymentSession.name)
		private readonly paymentSession: Model<PaymentSessionDocument>,
		@InjectModel(GiftCardOrder.name)
		private readonly giftCardOrder: Model<GiftCardOrderDocument>,
		private readonly userService: UserService,
		private readonly savedItemService: SavedItemService,
		private readonly razorpay: RazorpayService,
	) {}

	async getTotalGiftCardsCount() {
		return this.giftCard.countDocuments({ active: true }).exec()
	}
	async getAllGiftCards(
		queryParams: GetAllGiftCardsDto,
		userSession: ReqUser,
	): Promise<GetGiftCardListResponse> {
		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedGiftCards: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.GiftCard],
					user._id,
				)
			: []

		const page = queryParams.page ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const limit = queryParams.pageSize // Ensure pageSize is parsed correctly
		const sortQuery = buildSortQuery(
			queryParams.sortType,
			'createdAt',
			'name',
			'discountGetting',
		)
		const subCategoryIds = queryParams?.subCategories?.length
			? (queryParams?.subCategories?.split(',').map(Number) ?? [])
			: []

		const query = {
			name: { $regex: new RegExp(queryParams.searchParam, 'i') },
		}

		const aggregationPipeline = buildGiftCardAggregateQuery({
			subCategoryIds,
			sortQuery,
			skip,
			limit,
			queryConditions: query,
		}) as any

		const aggregationResult = await this.giftCard
			.aggregate(aggregationPipeline)
			.exec()

		if (
			!aggregationResult ||
			aggregationResult.length === 0 ||
			aggregationResult[0].paginatedResults.length === 0
		) {
			throw new NotFoundException('Gift Cards  not found')
		}

		const [result] = aggregationResult
		const giftCards = result.paginatedResults as GiftCardDocument[]
		const totalCount =
			result.totalCount.length > 0 ? result.totalCount[0].count : 0
		const banner = await this.banners.find({}).lean().exec()

		return {
			giftCards: giftCards.map(giftCard => {
				const isSaved = usersSavedGiftCards.some(
					savedGiftCard => savedGiftCard.itemUid === giftCard.uid,
				)
				const { uid, name, image, cashbackGiving, _id } = giftCard
				return {
					uid,
					id: _id,
					name,
					imageUrl: image.secureUrl,
					caption: `Up to ${cashbackGiving}% cashback`,
					saved: isSaved,
				}
			}),
			pagination: {
				page,
				pageSize: giftCards.length,
				total: totalCount,
			},
		}
	}

	async getGiftCardDetails(
		id: string,
		userSession: ReqUser,
	): Promise<GetGiftCardResponse> {
		const giftCard = (await this.giftCard
			.findOne({ uid: id })
			.lean()
			.populate('relatedInstantStore')
			.populate('relatedGiftCards')
			.exec()) as GiftCardDocument
		// console.log("🚀 ~ GiftCardService ~ getGiftCardDetails ~ giftCard:", giftCard?.howToUse)
		if (!giftCard) {
			throw new NotFoundException('Gift card not found')
		}

		const denominations = giftCard?.denominations ?? []
		// If denominations is empty, set the range between minimum and maximum amount in 50s
		// Round the minimum amount to the nearest 50 greater than or equal the minimum amount
		if (denominations.length === 0) {
			const min = Math.ceil(giftCard.minimumAmount / 50) * 50
			const max = Math.floor(giftCard.maximumAmount / 50) * 50
			for (let i = min; i <= max; i += 50) {
				denominations.push(i)
			}
		}

		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null

		const usersSavedGiftCards: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.GiftCard],
					user._id,
				)
			: []
		// FIXME After the reseed, the relatedGiftCards is not an array of string, it is an array of GiftCard
		const similarGiftCards = giftCard.relatedGiftCards.map(
			(relatedGiftCard: GiftCardDocument) => {
				const { uid, name, image, cashbackGiving, _id } = relatedGiftCard
				const isSaved = usersSavedGiftCards.some(
					savedGiftCard => uid === savedGiftCard.uid,
				)
				return {
					uid,
					id: _id,
					name,
					imageUrl: image?.secureUrl,
					caption: `Up to ${cashbackGiving}% cashback`,
					saved: isSaved,
				}
			},
		)

		const response: GiftCardDetails = {
			uid: giftCard.uid,
			id: giftCard._id,
			name: giftCard.name,
			storeName: giftCard?.relatedInstantStore?.name ?? '',
			storeUid: giftCard?.relatedInstantStore?.uid ?? 0,
			storeLogo: giftCard?.relatedInstantStore?.logo?.secureUrl ?? '',
			storeBgColor: giftCard?.relatedInstantStore?.bgColor ?? '',
			imageUrl: giftCard.image.secureUrl,
			description: giftCard.description,
			discountGetting: giftCard.discountGetting,
			cashbackGiving: giftCard.cashbackGiving,
			showCustomAmount: giftCard.isCustomDenomination,
			denominations,
			terms: giftCard.terms,
			howToUse: giftCard.howToUse,
		}
		return {
			giftCard: response,
			similarGiftCards,
		}
	}

	async getAllGiftCardBanners(): Promise<GiftCardBannersResponse> {
		const banners = await this.banners.find({ type: 'giftCard' }).exec()
		if (!banners) {
			throw new NotFoundException('Banners not found')
		}

		const response: GiftCardBannersResponse = {
			desktopBanners: banners.map((banner: BannerDocument) => {
				const { redirectUrl, termsContent, termsTitle, desktopBanner } = banner
				return {
					imageUrl: desktopBanner.secureUrl,
					redirectUrl,
					termsContent,
					termsTitle,
				}
			}),
			mobileBanners: banners.map((banner: BannerDocument) => {
				const { redirectUrl, termsContent, termsTitle, mobileBanner } = banner
				return {
					imageUrl: mobileBanner.secureUrl,
					redirectUrl,
					termsContent,
					termsTitle,
				}
			}),
		}
		return response
	}

	async getGiftCardsSearch(
		subCategoryNames: string[],
		search: string,
	): Promise<SearchGiftCardResponse> {
		const aggregationPipeline = []

		aggregationPipeline.push({
			$match: {
				categories: {
					$elemMatch: {
						subCategories: {
							$elemMatch: {
								name: { $in: subCategoryNames },
							},
						},
					},
				},
			},
		})
		aggregationPipeline.push({
			$facet: {
				giftCards: [
					{
						$match: {
							name: { $regex: search, $options: 'i' },
							active: true,
						},
					},
					{
						$project: {
							title: '$name',
							uid: 1,
							imageUrl: '$image.secureUrl',
							caption: {
								$concat: [
									'Up to ',
									{ $toString: '$cashbackGiving' }, // Convert the cashbackGiving field to a string
									'% cashback',
								],
							},
						},
					},
				],
			},
		})
		aggregationPipeline.push({
			$project: {
				giftCards: { $slice: ['$giftCards', 3] },
				giftCardsCount: { $size: '$giftCards' },
			},
		})

		const [results] = await this.giftCard.aggregate(aggregationPipeline).exec()

		return results
	}

	async getGiftCardByUid(uid: number) {
		return await this.giftCard.findOne({ uid }).exec()
	}

	async getGiftCardById(id: Types.ObjectId) {
		return await this.giftCard.findById(id).exec()
	}

	async saveGiftCard(saveParams: SaveOfferDto, userSession: ReqUser) {
		const user = (await this.userService.getUserByEmail(
			userSession.email,
		)) as UserDocument

		if (!user) {
			throw new NotFoundException('User not found')
		}

		const giftCard = await this.getGiftCardByUid(saveParams.itemUid)

		if (!giftCard) {
			throw new NotFoundException('Gift card not found')
		}

		await this.savedItemService.saveItem(user, SavedEnum.GiftCard, giftCard)
		return {
			message: 'Gift card saved successfully',
		}
	}

	async removeGiftCard(userSession: ReqUser, params: RemoveOfferDto) {
		const user = await this.userService.getUserByEmail(userSession.email)

		if (!user) {
			throw new NotFoundException('User not found')
		}

		// Remove the saved item
		await this.savedItemService.removeItem(
			user,
			SavedEnum.GiftCard,
			params.itemUid,
		)

		return {
			message: 'Gift card removed successfully.',
		}
	}

	async orderGiftCard(
		orderData: OrderGiftCardDto,
		user: ReqUser,
	): Promise<OrderResponse> {
		const giftCard = await this.getGiftCardById(orderData.giftcardId)
		if (!giftCard) {
			throw new NotFoundException('Gift card not found')
		}

		const isAvailableCards = orderData.cards.every(card =>
			giftCard.denominations.includes(card.amount),
		)

		if (!isAvailableCards && !giftCard.isCustomDenomination) {
			throw new NotAcceptableException(
				'One or more card amounts are not available in gift card denominations',
			)
		}

		const totalAmount = orderData.cards.reduce(
			(acc, card) => acc + card.amount * card.quantity,
			0,
		)

		if (
			giftCard?.minimumAmount &&
			giftCard.maximumAmount &&
			(totalAmount < giftCard.minimumAmount ||
				totalAmount > giftCard.maximumAmount)
		) {
			throw new NotAcceptableException(
				`Total amount should be between ${giftCard.minimumAmount} and ${giftCard.maximumAmount}`,
			)
		}
		let remainingAmount = totalAmount
		const paymentProcessors: PaymentProcessor[] = []

		const paymentSession = new this.paymentSession({
			user: user.id,
			lockedBalance: totalAmount,
		})

		if (orderData.paymentMethods.includes('balance')) {
			paymentProcessors.push(new BalancePaymentProcessor(this.userService))
		}
		if (orderData.paymentMethods.includes('razorpay')) {
			paymentProcessors.push(new RazorpayPaymentProcessor(this.razorpay))
		}
		let orderId = ''
		for (const processor of paymentProcessors) {
			if (remainingAmount <= 0) {
				break // Stop if the full amount has been processed
			}
			try {
				const processedAmount = await processor.processPayment(
					user,
					remainingAmount,
					orderData,
				)
				remainingAmount -=
					processedAmount.status === 'success' ? processedAmount.amount : 0
				if (processor.type === 'balance') {
					paymentSession.lockedBalance = processedAmount.amount
				}
				if (processor.type === 'razorpay' && processedAmount.order) {
					orderId = processedAmount.order.id
				}
			} catch (error) {
				throw new NotAcceptableException(error.message)
			}
		}
		await paymentSession.save()

		const icbOrder = new this.giftCardOrder<GiftCardOrder>({
			user: user.id,
			giftCard: giftCard.id,
			totalAmount,
			cards: orderData.cards,
			paymentType: orderData.paymentMethods,
			name: orderData.name,
			email: orderData.email,
			mobile: orderData.mobile,
			msg: orderData.msg,
			paymentSession: paymentSession.id,
			icbBalance: paymentSession.lockedBalance,
			orderId,
		})

		await icbOrder.save()

		if (
			orderData.paymentMethods.includes('balance') &&
			orderData.paymentMethods.length === 1 &&
			remainingAmount === 0
		) {
			if (giftCard.cardType === 'icb') {
				for (const card of orderData.cards) {
					for (let i = 0; i < card.quantity; i++) {
						await this.createIcbGiftCard({
							amount: card.amount,
							giftCard: giftCard._id,
							buyer: user.id,
							email: user.email,
							expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
							mobile: orderData.mobile,
							name: orderData.name,
							orderId: icbOrder.id,
							session: paymentSession,
							msg: orderData.msg,
						})
					}
				}
			}
			paymentSession.status = 'completed'
			await paymentSession.save()
			icbOrder.paymentVerified = true
			await icbOrder.save()
		}

		return {
			amountToPay: remainingAmount,
			orderId: remainingAmount === 0 ? '' : orderId,
			currency: 'INR',
			fullPaymentDone: remainingAmount === 0,
			paymentMethod: remainingAmount === 0 ? 'balance' : 'razorpay',
		}
	}

	private async createIcbGiftCard(dta: CreateIcbGiftCard) {
		const hashString = `${dta.buyer}${Date.now()}${dta.amount}${
			env.HASH.secret
		}`
		const icbGiftCard = new this.icbGiftCard({
			uid: 0,
			giftCardNumber: Number(
				BigInt(
					`${murmurHash(hashString, 1)}${murmurHash(hashString, 2)}`.substring(
						0,
						12,
					),
				),
			),
			giftCard: dta.giftCard,
			giftCardPin: Number(murmurHash(hashString, 3).toString().substring(0, 4)),
			amount: dta.amount,
			orderId: dta.orderId,
			expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
			buyer: dta.buyer,
			name: dta.name,
			email: dta.email,
			mobile: dta.mobile,
			msg: dta.msg,
		})

		await icbGiftCard.save()

		await sendGiftCardMail({
			amount: icbGiftCard.amount,
			cardName: 'ICB',
			email: icbGiftCard.email,
			expiryDate: icbGiftCard.expiryDate,
			giftCard: icbGiftCard.giftCardNumber,
			msg: icbGiftCard.msg,
			name: icbGiftCard.name,
			pin: icbGiftCard.giftCardPin,
		})

		dta.session.lockedBalance -= dta.amount
		await dta.session.save()
	}

	async verifyPayment(orderData: PaymentVerifyDto, user: ReqUser) {
		const order = await this.giftCardOrder
			.findOne({
				orderId: orderData.orderId,
				user: user.id,
			})
			.populate('paymentSession')
			.exec()

		if (!order) {
			throw new NotFoundException('Order not found')
		}

		if (order.paymentVerified) {
			return true
		}

		const paymentSession = order.paymentSession as PaymentSessionDocument

		if (paymentSession.status === 'completed') {
			return true
		}

		const isSignatureValid = await this.razorpay.verifyPaymentSignature(
			orderData.paymentId,
			orderData.orderId,
			orderData.signature,
		)

		if (!isSignatureValid) {
			throw new NotAcceptableException('Invalid payment signature')
		}

		const giftCard = await this.getGiftCardById(
			order.giftCard as Types.ObjectId,
		)

		if (!giftCard) {
			throw new NotFoundException('Gift card not found')
		}

		if (giftCard.cardType === 'icb') {
			for (const card of order.cards) {
				for (let i = 0; i < card.quantity; i++) {
					await this.createIcbGiftCard({
						amount: card.amount,
						giftCard: giftCard._id,
						buyer: user.id,
						email: user.email,
						expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
						mobile: order.mobile,
						name: order.name,
						orderId: order.id,
						session: paymentSession,
						msg: order.msg,
					})
				}
			}
		}

		paymentSession.status = 'completed'

		await paymentSession.save()

		order.paymentVerified = true

		await order.save()

		return true
	}

	async getIcbCard() {
		const icbCard = await this.giftCard.findOne({ cardType: 'icb' }).exec()
		if (!icbCard) {
			throw new NotFoundException('Icb Card not found')
		}
		return {
			id: icbCard.id,
			name: icbCard.name,
		}
	}

	async redeemIcbGiftCard(params: RedeemIcbGiftCardDto, user: ReqUser) {
		const giftCardNumber = params.giftCardNumber
		const giftCardPin = params.giftCardPin
		const icbGiftCard = await this.icbGiftCard.findOne({
			giftCardNumber,
			active: true,
			isRedeemed: false,
			expiryDate: { $gte: currentTime }, // Check if not expired
		})

		if (!icbGiftCard) {
			// If the gift card is not found
			throw new NotFoundException('Invalid or expired gift card.')
		}

		// Check if the provided gift card pin matches
		if (icbGiftCard.giftCardPin !== giftCardPin) {
			throw new NotAcceptableException('Invalid gift card pin.')
		}

		icbGiftCard.isRedeemed = true
		icbGiftCard.redeemDate = new Date()

		this.userService.updateGiftCardBalance(user.id, icbGiftCard.amount)

		await icbGiftCard.save()

		return true
	}

	async cancelExpiredSessions() {
		const threeMinutesAgo = new Date()
		const expiredSessions = await this.paymentSession
			.find({
				expiryDate: { $lte: threeMinutesAgo },
				status: 'active',
			})
			.exec()

		const promises = expiredSessions.map(async session => {
			session.status = 'expired'
			await this.userService.unlockUserBalance(
				session.user,
				session.lockedBalance,
			)
			await session.save()
		})
		await Promise.all(promises)
	}

	async getRedeemGiftCardHistory(
		queryParams: GetRedeemGiftCardsDto,
		user: ReqUser,
	) {
		const loggedUser = user.id
		const page = queryParams.page ?? 1
		const pageSize = queryParams.pageSize ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const limit = queryParams.pageSize // Ensure pageSize is parsed correctly
		const sortQuery = buildGiftCardRedeemHistorySortQuery(
			queryParams.sortType,
			'redeemDate',
			'amount',
		)

		const query = {
			name: { $regex: new RegExp(queryParams.searchParam, 'i') },
		}

		const aggregationPipeline = buildGiftCardRedeemHistoryAggregateQuery({
			loggedUser,
			sortQuery,
			skip,
			limit,
			queryConditions: query,
		}) as any

		const aggregationResult = await this.icbGiftCard
			.aggregate(aggregationPipeline)
			.exec()

		if (
			!aggregationResult ||
			aggregationResult.length === 0 ||
			aggregationResult[0].paginatedResults.length === 0
		) {
			throw new NotFoundException('Gift Card History list  not found')
		}

		const [result] = aggregationResult
		const icbGiftCards = result.paginatedResults as IcbGiftCardDocument[]
		const totalCount =
			result.totalCount.length > 0 ? result.totalCount[0].count : 0
		return {
			redeemGiftCards: icbGiftCards.map(icbGiftCard => {
				const { uid, amount, redeemDate, isRedeemed } = icbGiftCard

				const formattedRedeemDate = new Date(redeemDate)
					.toLocaleDateString('en-US', {
						year: 'numeric',
						month: 'short',
						day: '2-digit',
					})
					.toUpperCase()

				const formattedRedeemTime = new Date(redeemDate)
					.toLocaleTimeString('en-US', {
						hour: 'numeric',
						minute: '2-digit',
						hour12: true,
					})
					.toUpperCase()

				return {
					giftCardId: 'ICB123456',
					uid,
					amount: amount,
					date: formattedRedeemDate,
					time: formattedRedeemTime,
					paid: isRedeemed,
				}
			}),
			pagination: {
				page,
				pageSize: icbGiftCards.length,
				total: totalCount,
			},
		}
	}
}
