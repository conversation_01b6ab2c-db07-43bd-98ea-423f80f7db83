import { ApiProperty } from '@nestjs/swagger'
import {
	IsEmail,
	IsJWT,
	IsNotEmpty,
	IsOptional,
	IsString,
} from 'class-validator'

export class LoginDto {
	@ApiProperty({ example: '<EMAIL>', required: false })
	@IsEmail()
	@IsString()
	@IsOptional()
	email?: string

	@ApiProperty({ example: '123456789', required: false })
	@IsString()
	@IsNotEmpty()
	@IsOptional()
	mobile?: string
}

export class RefreshDto {
	@ApiProperty()
	@IsNotEmpty()
	@IsJWT()
	refreshToken!: string
}
