import { ApiProperty } from '@nestjs/swagger'
import {
	IsEmail,
	IsNumber,
	IsOptional,
	IsString,
	ValidateIf,
} from 'class-validator'

export class VerifyUserDto {
	@ApiProperty({
		example: '<EMAIL>',
		description: 'User Email',
		required: false,
	})
	@IsOptional()
	@ValidateIf(o => !o.mobile)
	@IsEmail()
	email?: string

	@ApiProperty({
		example: '9090888899',
		description: "User's mobile",
		required: false,
	})
	@IsOptional()
	@ValidateIf(o => !o.emai)
	@IsString()
	mobile?: string

	@ApiProperty({ example: 789876, description: 'User verification otp' })
	@IsNumber()
	otp: number
}
