import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'

import { Model } from 'mongoose'
import {
	HeroAccess,
	HeroAccessDocument,
} from 'shared/entities/hero-access.entity'
import {
	QuickAccess,
	QuickAccessDocument,
} from 'shared/entities/quick-access.entity'
import {
	HeroResponseItem,
	QuickAccessResponseItem,
} from '../types/quick-access.types'

@Injectable()
export class QuickAccessService {
	constructor(
		@InjectModel(QuickAccess.name)
		private quickAccess: Model<QuickAccessDocument>,
		@InjectModel(HeroAccess.name)
		private heroAccess: Model<HeroAccessDocument>,
	) {}

	async getActiveQuickAccess(): Promise<QuickAccessResponseItem[]> {
		const quickAccesses: QuickAccess[] = await this.quickAccess
			.find({
				active: true,
			})
			.exec()

		const quickAccessesResponse: QuickAccessResponseItem[] = quickAccesses.map(
			item => {
				return {
					title: item?.title || '',
					imageUrl: item?.icon.secureUrl || '',
					redirectUrl: item.redirectUrl || '',
				}
			},
		)

		return quickAccessesResponse
	}

	async getHeroQuickAccesses(): Promise<HeroResponseItem[]> {
		const heroAccesses: HeroAccess[] = await this.heroAccess
			.find({
				active: true,
			})
			.exec()

		const heroAccessesResponse: HeroResponseItem[] = heroAccesses.map(item => {
			return {
				title: item?.title || '',
				iconUrl: item?.icon.secureUrl || '',
				redirectUrl: item.redirectUrl || '',
			}
		})

		return heroAccessesResponse
	}
}
