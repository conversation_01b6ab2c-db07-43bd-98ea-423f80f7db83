import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
	TermsAndPrivacy,
	TermsAndPrivacyDocument,
} from '../../../shared/entities/terms-and-privacy.entity'
import { GetTermsAndPrivacyDto } from './dto/get-terms-and-privacy.dto'
@Injectable()
export class TermsAndPrivacyService {
	constructor(
		@InjectModel(TermsAndPrivacy.name)
		private readonly termsAndPrivacy: Model<TermsAndPrivacyDocument>,
	) {}

	async getAllTermsAndPrivacy(type: string) {
		const termsAndPrivacy = await this.termsAndPrivacy.findOne({ type })

		if (!termsAndPrivacy) {
			throw new NotFoundException('Terms and Privacy not found')
		}
		return termsAndPrivacy.content
	}
}
