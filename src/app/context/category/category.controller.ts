import { Controller, Get, Param, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { AuthOptional, OptionalUser, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import {
	AllCategoriesResponse,
	CategoryResponse,
	SubCategoriesByCategory,
	SubCategoriesByCategoryResponse,
} from './category.dto'
import { CategoryService } from './category.service'

@ApiTags('Context')
@Controller('context/category')
export class CategoryController {
	constructor(private readonly categoryService: CategoryService) {}

	@ApiResponse({
		type: [CategoryResponse],
	})
	@Get()
	async getAllCategories(@Query('trending') trending: boolean) {
		return this.categoryService.getAllCategories(trending)
	}

	@ApiResponse({
		type: SubCategoriesByCategoryResponse,
	})
	@AuthOptional()
	@Get('sub-category:id')
	async getSubCategoryByCategoryId(
		@Param('id') id: string,
		@OptionalUser() userSession: ReqUser,
	) {
		return await this.categoryService.getSubCategoryByCategoryId(
			id,
			userSession,
		)
	}

	@ApiResponse({
		type: [SubCategoriesByCategory],
	})
	@AuthOptional()
	@Get('search:value')
	async getSubCategorySearch(@Param('value') value: string) {
		return await this.categoryService.getSubCategorySearch(value)
	}

	@ApiResponse({
		type: [AllCategoriesResponse],
	})
	@Get('all-categories-details')
	async getAllCategoriesDetails() {
		return await this.categoryService.getAllCategoriesDetails()
	}
}
