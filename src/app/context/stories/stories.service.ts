import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import {
	MobileStory,
	MobileStoryDocument,
} from 'shared/entities/mobile-stories.entity'
import { currentTime } from 'shared/helpers/time.helper'
import { ResponseMobileStories } from '../types/stories.types'

@Injectable()
export class StoriesService {
	constructor(
		@InjectModel(MobileStory.name)
		private stories: Model<MobileStoryDocument>,
	) {}

	async getStories(): Promise<ResponseMobileStories[]> {
		//set contentType as 'image' as default

		const contentType = 'image'

		// Aggregate stories and group them by store
		const storiesByStore: ResponseMobileStories[] = await this.stories
			.aggregate([
				{ $match: { active: true, expiryDate: { $gt: currentTime } } }, // Filter active stories
				{
					$lookup: {
						from: 'stores',
						localField: 'store',
						foreignField: '_id',
						as: 'store',
					},
				}, // Populate store information
				{ $unwind: '$store' }, // Unwind the store array

				{
					$group: {
						_id: '$store._id', // Group by the name field of the store
						storeName: { $first: '$store.name' }, // Use $first to get the store name since all entries in a group are from the same store
						storeLogo: { $first: { $ifNull: ['$store.logo.secureUrl', null] } }, // Get the store logo URL
						storeBgColor: { $first: { $ifNull: ['$store.bgColor', null] } }, // Get the store bg color
						stories: {
							$push: {
								imageUrl: '$image.secureUrl',
								type: contentType,
								duration: '$duration',
								timestamp: { $toLong: { $toDate: '$updatedAt' } },
								title: '$title',
								description: '$description',
								redirectUrl: {
									$cond: [
										{ $ifNull: ['$redirectUrl', false] },
										'$redirectUrl',
										'$$REMOVE',
									], // Remove if redirectUrl don't exist'
								},
								buttonText: {
									$cond: [
										{ $ifNull: ['$redirectUrl', false] },
										'$buttonText',
										'$$REMOVE',
									], // Remove if redirectUrl don't exist
								},
							},
						}, // Push the selected fields into an array
					},
				},
			])
			.exec()

		return storiesByStore.map(store => {
			return {
				storeName: store.storeName,
				storeLogo: store.storeLogo,
				storeBgColor: store.storeBgColor,
				stories: store.stories,
			}
		})
	}
}
