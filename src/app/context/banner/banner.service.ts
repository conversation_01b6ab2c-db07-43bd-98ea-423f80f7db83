import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import {
	convertLocalToUTC,
	convertLocalToUTCDate,
	currentTime,
} from 'shared/helpers/time.helper'
import { Banner, BannerDocument } from '../../../shared/entities/banner.entity'
import { BannerResponse } from '../types/banner.types'

@Injectable()
export class BannerService {
	constructor(
		@InjectModel(Banner.name)
		private banners: Model<BannerDocument>,
	) {}

	async getActiveBannerUrls(): Promise<BannerResponse> {
		const activeBanners: Banner[] = await this.banners
			.find({
				active: true,
				expiryDate: { $gt: convertLocalToUTCDate() }, // Filters banners with expiryDate greater than the current date
			})
			.sort({ priority: -1 }) // Sort by priority in descending order
			.exec()

		const bannerImages: BannerResponse = {
			desktopBanners: [],
			mobileBanners: [],
		}
		// Extract secure URLs & redirect URLs from active banners
		for (const banner of activeBanners) {
			bannerImages.desktopBanners.push({
				imageUrl: banner.desktopBanner.secureUrl || '',
				redirectUrl: banner.redirectUrl || '',
			})
			bannerImages.mobileBanners.push({
				imageUrl: banner.mobileBanner.secureUrl || '',
				redirectUrl: banner.redirectUrl || '',
			})
		}
		return bannerImages
	}
}
