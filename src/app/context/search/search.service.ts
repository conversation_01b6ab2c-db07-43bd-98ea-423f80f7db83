import { Injectable } from '@nestjs/common'
import { GiftCardService } from 'app/gift-card/gift-card.service'
import { Types } from 'mongoose'
import { isEmpty } from 'radash'
import { MeiliSearchService } from 'shared/modules/meilisearch'
import { OffersService } from '../offers/offers.service'
import { StoresService } from '../stores/stores.service'
import { SearchResponseItem } from '../types/search.types'
@Injectable()
export class SearchService {
	constructor(
		private readonly storeService: StoresService,
		private readonly offerService: OffersService,
		private readonly giftCardService: GiftCardService,
	) {}

	async getSearchResults(search: string) {
		const searchCategoryNames = [
			'100% Cashback Stores',
			'50% Cashback Stores',
			'25% Cashback Stores',
		]
		const store =
			search.length > 0
				? await this.storeService.getStoresForSearch(
						searchCategoryNames,
						search,
					)
				: {
						stores: [],
						storesCount: 0,
					}
		const { deal, coupon } =
			search.length > 0
				? await this.offerService.getDealsAndCouponsSearch(
						searchCategoryNames,
						search,
					)
				: {
						deal: {
							deals: [],
							dealsCount: 0,
						},
						coupon: {
							coupons: [],
							couponsCount: 0,
						},
					}

		const giftCard =
			search.length > 0
				? await this.giftCardService.getGiftCardsSearch(
						searchCategoryNames,
						search,
					)
				: {
						giftCards: [],
						giftCardsCount: 0,
					}

		return {
			store,
			deal,
			coupon,
			giftCard,
		}
	}

	async searchResults(search: string) {
		const client = new MeiliSearchService()
		const { hits: response } = await client.searchDocuments(search)

		const storeDta = response
			.filter(item => item.type === 'store' && item.active)
			.slice(0, 5)
		const giftCardDta = response
			.filter(item => item.type === 'giftCard')
			.slice(0, 5)
		const couponsDta = response
			.filter(item => item.type === 'offer' && !isEmpty(item.couponCode))
			.slice(0, 5)
		const dealsDta = response.filter(item => item.type === 'offer').slice(0, 5)
		const stores = {
			total: 0,
			storeList: [],
		} as {
			total: number
			storeList: {
				url: string
				name: string
				uid: number
				count: number
			}[]
		}
		for (const item of storeDta) {
			const offersCount = await this.offerService.getOffersCountByStoreId([
				new Types.ObjectId(item.id),
			])
			stores.storeList.push({
				url: item.url,
				name: item.name as string,
				uid: item.uid,
				count: offersCount.length > 0 ? offersCount[0].count : 0,
			})
		}
		// Set total to the count of stores that match the search query
		stores.total = response.filter(
			item => item.type === 'store' && item.active,
		).length

		const giftCards = {
			total: 0,
			giftCardList: [],
		} as {
			total: number
			giftCardList: {
				url: string
				name: string
				uid: number
				caption: string
			}[]
		}

		for (const item of giftCardDta) {
			giftCards.giftCardList.push({
				url: item.url,
				name: item.name as string,
				uid: item.uid,
				caption: `Up to ${item.cashbackGiving}% cashback`,
			})
		}
		giftCards.total = response.filter(item => item.type === 'giftCard').length

		const coupons = {
			total: 0,
			couponList: [],
		} as {
			total: number
			couponList: {
				url: string
				name: string
				title: string
				uid: number
				newUserOffer: string
				oldUserOffer: string
				couponCode: string
				caption: string
			}[]
		}

		for (const item of couponsDta) {
			coupons.couponList.push({
				url: item.url,
				name: item.name as string,
				title: item.title as string,
				uid: item.uid,
				newUserOffer: item.newUserOffer as string,
				oldUserOffer: item.oldUserOffer as string,
				couponCode: item.couponCode as string,
				caption: item.caption,
			})
		}

		coupons.total = response.filter(
			item => item.type === 'offer' && !isEmpty(item.couponCode),
		).length

		const deals = {
			total: 0,
			dealList: [],
		} as {
			total: number
			dealList: {
				url: string
				name: string
				title: string
				uid: number
				newUserOffer: string
				oldUserOffer: string
				caption: string
			}[]
		}

		for (const item of dealsDta) {
			deals.dealList.push({
				url: item.url,
				name: item.name as string,
				uid: item.uid,
				title: item.title as string,
				newUserOffer: item.newUserOffer as string,
				oldUserOffer: item.oldUserOffer as string,
				caption: item.caption,
			})
		}

		deals.total = response.filter(
			item => item.type === 'offer' && isEmpty(item.couponCode),
		).length

		return {
			stores,
			giftCards,
			coupons,
			deals,
		}
	}
}
