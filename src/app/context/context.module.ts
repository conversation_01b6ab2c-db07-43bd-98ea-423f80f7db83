import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { GiftCardModule } from 'app/gift-card/gift-card.module'
import { SavedItemModule } from 'app/saved-item/saved-item.module'
import { UserModule } from 'app/user/user.module'
import {
	Banner,
	BannerSchema,
	Categories,
	CategoriesSchema,
	Category,
	CategorySchema,
	GiftCard,
	GiftCardSchema,
	MobileStory,
	MobileStorySchema,
	Offer,
	OfferSchema,
	PersonalInterest,
	PersonalInterestSchema,
	QuickAccess,
	QuickAccessSchema,
	SavedItem,
	SavedItemSchema,
	Store,
	StoreSchema,
	SubCategory,
	SubCategorySchema,
	TermsAndPrivacy,
	TermsAndPrivacySchema,
	Testimonial,
	TestimonialSchema,
} from 'shared/entities'
import {
	HeroAccess,
	HeroAccessSchema,
} from 'shared/entities/hero-access.entity'
import {
	MissedDeals,
	MissedDealsSchema,
} from 'shared/entities/missed-deals.entity'
import {
	OngoingSaleOffers,
	OngoingSaleOffersSchema,
} from 'shared/entities/ongoing-sale-offers.entity'
import { BannerController } from './banner/banner.controller'
import { BannerService } from './banner/banner.service'
import { CategoryController } from './category/category.controller'
import { CategoryService } from './category/category.service'
import { OffersController } from './offers/offers.controller'
import { OffersService } from './offers/offers.service'
import { PersonalInterestService } from './personal-interest/personal-interest.service'
import { QuickAccessController } from './quick-access/quick-access.controller'
import { QuickAccessService } from './quick-access/quick-access.service'
import { SearchController } from './search/search.controller'
import { SearchService } from './search/search.service'
import { StoresController } from './stores/stores.controller'
import { StoresService } from './stores/stores.service'
import { StoriesController } from './stories/stories.controller'
import { StoriesService } from './stories/stories.service'
import { TermsAndPrivacyController } from './terms-and-privacy/terms-and-privacy.controller'
import { TermsAndPrivacyService } from './terms-and-privacy/terms-and-privacy.service'
import { TestimonialController } from './testimonial/testimonial.controller'
import { TestimonialService } from './testimonial/testimonial.service'

@Module({
	imports: [
		SavedItemModule,
		UserModule,
		GiftCardModule,
		MongooseModule.forFeature([
			{
				name: SavedItem.name,
				schema: SavedItemSchema,
			},
			{
				name: Banner.name,
				schema: BannerSchema,
			},

			{
				name: MobileStory.name,
				schema: MobileStorySchema,
			},
			{
				name: Category.name,
				schema: CategorySchema,
			},
			{
				name: SubCategory.name,
				schema: SubCategorySchema,
			},
			{
				name: Categories.name,
				schema: CategoriesSchema,
			},
			{
				name: QuickAccess.name,
				schema: QuickAccessSchema,
			},
			{
				name: HeroAccess.name,
				schema: HeroAccessSchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: Offer.name,
				schema: OfferSchema,
			},
			{
				name: MissedDeals.name,
				schema: MissedDealsSchema,
			},
			{
				name: OngoingSaleOffers.name,
				schema: OngoingSaleOffersSchema,
			},
			{
				name: GiftCard.name,
				schema: GiftCardSchema,
			},
			{
				name: TermsAndPrivacy.name,
				schema: TermsAndPrivacySchema,
			},
			{
				name: PersonalInterest.name,
				schema: PersonalInterestSchema,
			},
			{
				name: Testimonial.name,
				schema: TestimonialSchema,
			},
		]),
	],
	controllers: [
		BannerController,
		QuickAccessController,
		StoriesController,
		OffersController,
		CategoryController,
		StoresController,
		SearchController,
		TermsAndPrivacyController,
		TestimonialController,
	],
	providers: [
		BannerService,
		QuickAccessService,
		StoriesService,
		OffersService,
		CategoryService,
		StoresService,
		SearchService,
		TermsAndPrivacyService,
		PersonalInterestService,
		TestimonialService,
	],
	exports: [PersonalInterestService],
})
export class ContextModule {}
