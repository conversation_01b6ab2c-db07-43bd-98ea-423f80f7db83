import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { convertIstToUtc } from 'app/user/user.helper'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import {
	Offer,
	OfferDocument,
	ReqUser,
	SavedItemDocument,
} from 'shared/entities'
import {
	MissedDeals,
	MissedDealsDocument,
} from 'shared/entities/missed-deals.entity'
import {
	OngoingSaleOffers,
	OngoingSaleOffersDocument,
} from 'shared/entities/ongoing-sale-offers.entity'
import { generateOfferCaption } from 'shared/helpers/offer.helper'
import {
	convertLocalToUTC,
	convertLocalToUTCDate,
	currentTime,
	getFinalDateToShow,
} from 'shared/helpers/time.helper'
import { SavedEnum } from 'shared/types'
import {
	ContextMissedOfferType,
	ContextOfferCouponsType,
	ContextOfferDealsType,
	ContextOngoingOfferType,
	GetAllOnGoingOffersResponse,
} from '../types/offers.types'

@Injectable()
export class OffersService {
	constructor(
		@InjectModel(Offer.name)
		private offers: Model<OfferDocument>,

		@InjectModel(MissedDeals.name)
		private missedDeals: Model<MissedDealsDocument>,

		@InjectModel(OngoingSaleOffers.name)
		private ongoingSales: Model<OngoingSaleOffersDocument>,

		private readonly savedItemService: SavedItemService,

		private readonly userService: UserService,
	) {}

	async getTotalOffersCount(type: 'all' | 'coupons') {
		const query =
			type === 'all'
				? {
						active: true,
						dateExpiry: { $gt: convertLocalToUTCDate() },
					}
				: {
						couponCode: {
							$exists: true,
						},
						active: true,
						dateExpiry: { $gt: convertLocalToUTCDate() },
					}
		return this.offers.countDocuments(query).exec()
	}

	async getLandingOffers(userSession?: ReqUser, timezone?: string) {
		const user = userSession
			? await this.userService.getUserByEmail(userSession?.email)
			: null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType(
					[SavedEnum.Offer],
					user._id,
				)
			: []
		const maxItemsPerCategory = 15

		let totalOffers = 0 // Variable to keep track of total offers retrieved

		const ongoingOffers: OngoingSaleOffers[] = [] // Initialize the array to store ongoing offers

		// Fetch ongoing offers with a limit of 15
		for await (const ongoingSale of this.ongoingSales.find({
			saleEndDate: { $gt: convertLocalToUTC() },
			// saleEndDate: { $gt: currentTime },
		})) {
			const remainingOffers = maxItemsPerCategory - totalOffers // Calculate remaining offers allowed to fetch
			const offersToFetch = Math.min(remainingOffers, maxItemsPerCategory) // Determine the number of offers to fetch for this ongoing sale

			// Populate offers for the current ongoing sale
			const populatedOngoingSale = await this.ongoingSales.populate(
				ongoingSale,
				{
					path: 'offers',
					model: 'Offer',
					options: { limit: offersToFetch }, // Limit the number of offers for this ongoing sale
					populate: {
						path: 'store',
						model: 'Store',
					},
				},
			)

			// Add the populated ongoing sale to the array
			ongoingOffers.push(populatedOngoingSale)

			// Update the total number of offers retrieved
			totalOffers += populatedOngoingSale.offers.length

			// Check if the total number of offers retrieved has reached the limit
			if (totalOffers >= maxItemsPerCategory) {
				break // Stop fetching more offers once the limit is reached
			}
		}

		// Fetch trending offers with a limit of 15
		const trendingDeals: Offer[] = await this.offers
			.find({
				trending: true,
				dateExpiry: { $gt: convertLocalToUTC() },
				active: true,
			}) // Filter expired offers
			.limit(maxItemsPerCategory) // Limit to 15 results
			.populate('store')
			.sort({ trendingPriority: -1, dateExpiry: -1 })
			.exec()

		// Fetch missed offers with a limit of 15
		const missedDeals: Offer[] = await this.offers
			.find({
				missedDeal: true, // Only consider offers marked as missed deals
				$or: [
					{ dateExpiry: { $lt: convertLocalToUTC() } }, // Expired offers
					{ active: false }, // Deleted (inactive) offers
				],
			})
			.limit(maxItemsPerCategory) // Limit to 15 results
			.populate('store')
			.sort({ priority: -1, dateExpiry: -1 }) // Sort by priority, then by expiry date
			.exec()

		const formattedTrendingDeal: ContextOfferDealsType[] = trendingDeals.map(
			offer => {
				// Check if the offer is saved by the user based on uid
				const saved = usersSavedOffers.some(
					savedItem => savedItem.itemUid === offer.uid,
				)

				return {
					uid: offer?.uid,
					productImage: offer?.productImage?.secureUrl,
					storeLogoUrl: offer?.store?.logo?.secureUrl,
					storeBgColor: offer?.store?.bgColor,
					storeName: offer?.store?.name,
					endDate:
						offer?.repeatBy === 'true'
							? getFinalDateToShow(offer.dateExpiry as unknown as string)
							: (offer?.dateExpiry as unknown as string),
					offerTitle: offer?.title,
					// ...(offer?.offer && offer?.offer.trim() !== ''
					// 	? { offerCaption: offer?.offer }
					// 	: {}),
					offerCaption: generateOfferCaption({
						offerType: offer?.offerType,
						offerAmount: offer?.offerAmount,
						offerPercent: offer?.offerPercent,
						cashbackType: offer?.store?.cashbackType,
					}),
					salePrice: offer.itemPrice ? offer?.itemPrice : 0,
					...(offer.migrated ? { offerUrl: offer?.url } : {}),
					saved: saved,
					isAutoGenerated: offer?.isAutoGenerated,
					hideCbTag: offer?.hideCbTag,
				}
			},
		)

		const trendingCoupons: Offer[] = trendingDeals.filter(
			offer => !!offer.couponCode,
		)

		const formattedTrendingCoupons: ContextOfferCouponsType[] =
			trendingCoupons.map(offer => {
				const saved = usersSavedOffers.some(
					savedItem => savedItem.itemUid === offer.uid,
				)
				return {
					uid: offer?.uid,
					productImage: offer?.productImage?.secureUrl,
					storeLogoUrl: offer?.store?.logo?.secureUrl,
					storeBgColor: offer?.store?.bgColor,
					storeName: offer?.store?.name,
					endDate:
						offer?.repeatBy === 'true'
							? getFinalDateToShow(offer.dateExpiry as unknown as string)
							: (offer?.dateExpiry as unknown as string),
					offerTitle: offer?.title,
					// ...(offer?.offer && offer?.offer.trim() !== ''
					// 	? { offerCaption: offer?.offer }
					// 	: {}),
					offerCaption: generateOfferCaption({
						offerType: offer?.offerType,
						offerAmount: offer?.offerAmount,
						offerPercent: offer?.offerPercent,
						cashbackType: offer?.store?.cashbackType,
					}),
					salePrice: offer.itemPrice ? offer?.itemPrice : 0,
					couponCode: offer?.couponCode as string,
					saved: saved,
					...(offer.migrated ? { offerUrl: offer?.url } : {}),
					isAutoGenerated: offer?.isAutoGenerated
						? offer?.isAutoGenerated
						: false,
					hideCbTag: offer?.hideCbTag ? offer.hideCbTag : false,
				}
			})

		const formattedMissedDeals: ContextMissedOfferType[] = missedDeals.map(
			offer => {
				return {
					uid: offer?.uid,
					productImage: offer?.productImage?.secureUrl,
					storeLogoUrl: offer?.store?.logo?.secureUrl,
					storeBgColor: offer?.store?.bgColor,
					storeName: offer?.store?.name,
					offerTitle: offer?.title,
					currentAmount: offer?.itemPrice,
					priority: offer?.priority ?? 0,
					...(offer.migrated ? { offerUrl: offer?.url } : {}),
				}
			},
		)

		const formattedOngoingOffers: ContextOngoingOfferType[] = ongoingOffers
			.flatMap(ongoingOffer =>
				ongoingOffer.offers.map(offer => {
					const saved = usersSavedOffers.some(
						savedItem => savedItem.itemUid === offer.uid,
					)
					return {
						uid: offer.uid,
						productImage: offer.productImage?.secureUrl ?? '',
						storeLogoUrl: offer.store?.logo?.secureUrl,
						storeBgColor: offer?.store?.bgColor,
						storeName: offer?.store?.name,
						endDate:
							offer?.repeatBy === 'true'
								? getFinalDateToShow(offer.dateExpiry as unknown as string)
								: (offer?.dateExpiry as unknown as string),
						offerTitle: offer.title,
						offerCaption: generateOfferCaption({
							offerType: offer?.offerType,
							offerAmount: offer?.offerAmount,
							offerPercent: offer?.offerPercent,
							cashbackType: offer?.store?.cashbackType,
						}),
						salePrice: offer.itemPrice ? offer?.itemPrice : 0,
						// (offer?.itemPrice as number) - (offer?.discount as number), // Type assertion
						saleLogoUrl: ongoingOffer?.saleLogo?.secureUrl,
						saleCaption: ongoingOffer.saleName,
						priority: offer.priority ?? 0,
						saved: saved,
						...(offer.migrated ? { offerUrl: offer?.url } : {}),
						isAutoGenerated: offer?.isAutoGenerated,
						hideCbTag: offer?.hideCbTag,
					}
				}),
			)
			.sort((a, b) => b.priority - a.priority)
			.map(({ priority, ...rest }) => rest)

		const categorizedOffers = {
			trendingOffers: {
				deals: formattedTrendingDeal,
				coupons: formattedTrendingCoupons,
			},
			ongoingOffers: formattedOngoingOffers,
			expiredOffers: formattedMissedDeals,
		}

		return categorizedOffers
	}

	async getOnGoingSaleOffers(): Promise<GetAllOnGoingOffersResponse[]> {
		const allOnGoingSaleOffers: OngoingSaleOffers[] = await this.ongoingSales
			.find({
				saleEndDate: { $gt: currentTime },
			})
			.populate('offers')
			.exec()
		return allOnGoingSaleOffers.map(ongoingSale => {
			return {
				uid: ongoingSale.uid,
				name: ongoingSale.saleName ?? '',
			}
		})
	}

	async getDealsAndCouponsSearch(subCategoryNames: string[], search: string) {
		const aggregationPipeline = []

		aggregationPipeline.push({
			$lookup: {
				from: 'stores',
				localField: 'store',
				foreignField: '_id',
				as: 'store',
			},
		})

		aggregationPipeline.push({
			$lookup: {
				from: 'storecategories',
				localField: 'storeCategory',
				foreignField: '_id',
				as: 'storeCategory',
			},
		})

		aggregationPipeline.push({
			$match: {
				categories: {
					$elemMatch: {
						subCategories: {
							$elemMatch: {
								name: { $in: subCategoryNames },
							},
						},
					},
				},
			},
		})
		aggregationPipeline.push({
			$facet: {
				deals: [
					{
						$match: {
							title: { $regex: search, $options: 'i' },
							active: true,
						},
					},
					{
						$project: {
							uid: 1,
							productImage: '$productImage.secureUrl',
							offerTitle: '$title',
							storeName: { $arrayElemAt: ['$store.name', 0] },
							newUserPercent: {
								$arrayElemAt: ['$storeCategory.gettingNewUserRate', 0],
							},
							oldUserPercent: {
								$arrayElemAt: ['$storeCategory.gettingOldUserRate', 0],
							},
							offerCaption: '$offer',
							offerAmount: 1,
						},
					},
				],
				coupons: [
					{
						$match: {
							title: { $regex: search, $options: 'i' },
							active: true,
							couponCode: { $exists: true },
						},
					},
					{
						$project: {
							uid: 1,
							productImage: '$productImage.secureUrl',
							offerTitle: '$title',
							storeName: { $arrayElemAt: ['$store.name', 0] },
							newUserPercent: {
								$arrayElemAt: ['$storeCategory.gettingNewUserRate', 0],
							},
							oldUserPercent: {
								$arrayElemAt: ['$storeCategory.gettingOldUserRate', 0],
							},
							offerCaption: '$offer',
							offerAmount: 1,
							itemPrice: 1,
						},
					},
				],
			},
		})
		aggregationPipeline.push({
			$project: {
				coupons: { $slice: ['$coupons', 3] },
				couponsCount: { $size: '$coupons' },
				deals: { $slice: ['$deals', 3] },
				dealsCount: { $size: '$deals' },
			},
		})

		const [results] = await this.offers.aggregate(aggregationPipeline)

		return {
			coupon: {
				coupons: results.coupons,
				couponsCount: results.couponsCount,
			},
			deal: { deals: results.deals, dealsCount: results.dealsCount },
		}
	}

	async getLatestOfferByStoreId(storeId: Types.ObjectId) {
		const latestOffer = await this.offers
			.findOne({
				store: storeId,
				dateExpiry: { $gt: convertLocalToUTCDate() },
			})
			.sort({ priority: -1, dateExpiry: -1 })
			.exec()
		return latestOffer
	}

	async getOffersCountByStoreId(storeIds: Types.ObjectId[]) {
		return await this.offers.aggregate([
			{
				$match: {
					store: { $in: storeIds }, // Array of store IDs
					dateExpiry: { $gt: convertLocalToUTCDate() },
					active: true,
				},
			},
			// Join with the stores collection to fetch store details
			{
				$lookup: {
					from: 'stores', // Assuming 'stores' is the name of your stores collection
					localField: 'store', // Field in offers collection that holds the reference to the store
					foreignField: '_id', // Field in stores collection that matches the reference
					as: 'storeDetails',
				},
			},
			// Unwind the storeDetails to make it easier to work with
			{
				$unwind: {
					path: '$storeDetails',
					preserveNullAndEmptyArrays: false, // Adjust based on your data - set to true if you want to keep offers without stores
				},
			},
			// Group by store ID and include the store UID in the result
			{
				$group: {
					_id: '$store', // Group by store ID
					uid: { $first: '$storeDetails.uid' }, // Get the UID from the first document in each group
					count: { $sum: 1 }, // Count the documents for each store
				},
			},
		])
	}
}

//test
//test2
