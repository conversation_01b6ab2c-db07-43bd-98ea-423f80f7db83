import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { Testimonial, TestimonialDocument } from 'shared/entities'
import { TestimonialResponseType } from '../types/testimonial.types'
@Injectable()
export class TestimonialService {
	constructor(
		@InjectModel(Testimonial.name)
		private readonly testimonial: Model<TestimonialDocument>,
	) {}

	async getAllTestimonials(): Promise<TestimonialResponseType[]> {
		const testimonials = await this.testimonial
			.find(
				{
					active: true,
				},
				{
					_id: 0,
					reviewerName: 1,
					'reviewerAvatar.secureUrl': 1,
					review: 1,
					rating: 1,
				},
			)
			.exec()

		if (!testimonials) {
			throw new NotFoundException('Testimonials not found')
		}

		const allTestimonials = testimonials.map(testimonial => {
			return {
				reviewerName: testimonial.reviewerName,
				reviewerAvatar: testimonial.reviewerAvatar.secureUrl,
				review: testimonial.review,
				rating: testimonial.rating,
			}
		})

		return allTestimonials
	}
}
