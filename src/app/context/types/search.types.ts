import { ApiProperty } from '@nestjs/swagger'

export class SearchResponseType {
	@ApiProperty()
	uid: number

	@ApiProperty()
	count: number

	@ApiProperty()
	url: string

	@ApiProperty()
	title: string

	@ApiProperty()
	name: string

	@ApiProperty()
	newUserOffer: string

	@ApiProperty()
	oldUserOffer: string

	@ApiProperty()
	caption: string

	@ApiProperty()
	couponCode: string
}

export class SearchStoreResponse {
	@ApiProperty({ type: [SearchResponseType] })
	storeList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchDealResponse {
	@ApiProperty({ type: [SearchResponseType] })
	dealList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchCouponResponse {
	@ApiProperty({ type: [SearchResponseType] })
	couponList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchGiftCardResponse {
	@ApiProperty({ type: [SearchResponseType] })
	giftCardList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchResponseItem {
	@ApiProperty({ type: SearchStoreResponse })
	stores: SearchStoreResponse

	@ApiProperty({ type: SearchDealResponse })
	deals: SearchDealResponse

	@ApiProperty({ type: SearchCouponResponse })
	coupons: SearchCouponResponse

	@ApiProperty({ type: SearchGiftCardResponse })
	giftCards: SearchGiftCardResponse
}
