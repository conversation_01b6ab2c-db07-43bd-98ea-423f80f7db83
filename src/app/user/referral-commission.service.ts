import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { u } from 'magic-regexp/dist/shared/magic-regexp.8360a3b8'
import { Model, PipelineStage, Types } from 'mongoose'
import { hash } from 'ohash'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { User, UserDocument } from 'shared/entities/user.entity'
import { GetReferralLeaderboardResponse } from './dto/leader-board'

@Injectable()
export class ReferralCommissionService {
	constructor(
		@InjectModel(User.name)
		private user: Model<UserDocument>,

		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>,
	) {}

	/**
	 * Process referral commission when a user earns cashback
	 * Gives 10% of cashback to the referrer
	 * @param userId User who earned cashback
	 * @param cashbackAmount The amount of cashback earned
	 * @param earningId The ID of the earning record
	 */
	async processReferralCommission(
		userId: Types.ObjectId,
		cashbackAmount: number,
		earningId: Types.ObjectId,
	): Promise<void> {
		// Find the user who earned cashback
		const user = await this.user.findById(userId)

		// If user has no referrer, exit
		if (!user || !user.referral) {
			return
		}

		// Get referrer details
		const referrer = await this.user.findById(user.referral)
		if (!referrer) {
			return
		}

		// Calculate 10% commission
		const commissionAmount = cashbackAmount * 0.1

		// Update the earning record with the referral commission
		await this.earning.findByIdAndUpdate(earningId, {
			referralCommission: commissionAmount,
		})

		// Check if user has reached Rs. 200 in total earnings
		if (user.totalEarned >= 200) {
			// Move pending rewards to actual rewards for both user and referrer
			if (user.pendingRewardPoints > 0) {
				await this.user.findByIdAndUpdate(userId, {
					$inc: { rewardPoints: user.pendingRewardPoints },
					pendingRewardPoints: 0,
				})
			}

			// Also update referrer's pending rewards if they exist
			if (referrer.pendingRewardPoints > 0) {
				await this.user.findByIdAndUpdate(user.referral, {
					$inc: { rewardPoints: referrer.pendingRewardPoints },
					pendingRewardPoints: 0,
				})
			}
		}
	}

	/**
	 * Checks if the campaign period is active
	 * @returns boolean indicating if the signup bonuses should be applied
	 */
	isCampaignActive(): boolean {
		// Define campaign start and end dates
		const campaignStart = new Date('2025-01-01T00:00:00Z') // Example: June 1, 2024
		const campaignEnd = new Date('2025-08-30T23:59:59Z') // Example: June 30, 2024

		const now = new Date()

		return now >= campaignStart && now <= campaignEnd
	}

	async createJoinEarning({
		userId,
		referralEarnings,
		newUserId,
		newUserEarnings,
	}: {
		userId: Types.ObjectId | null | undefined
		referralEarnings: number | null | undefined
		newUserId: Types.ObjectId
		newUserEarnings: number
	}): Promise<void> {
		if (
			userId &&
			referralEarnings &&
			referralEarnings > 0 &&
			this.isCampaignActive()
		) {
			// Create earning record for referrer
			const userEarning = new this.earning({
				uid: 0,
				user: new Types.ObjectId(userId),
				referralUser: new Types.ObjectId(newUserId),
				cashbackAmount: referralEarnings,
				earningsType: 'referral',
				status: 'pending',
				notes: 'Referral commission for new user signup bonus',
				referenceId: `CBERN${hash({
					user: userId,
					referralUser: newUserId,
					referralEarnings,
				}).toUpperCase()}`,
			})
			await userEarning.save()
			const user = (await this.user.findById(userId)) as UserDocument
			user.pendingBalance += referralEarnings
			await user.save()
		}

		if (newUserEarnings > 0 && this.isCampaignActive()) {
			// Create earning record for referred user
			const newUserEarning = new this.earning({
				uid: 0,
				user: new Types.ObjectId(newUserId),
				cashbackAmount: newUserEarnings,
				earningsType: 'referral',
				status: 'confirmed',
				notes: 'Signup bonus for joining',
				referenceId: `CBERN${hash({
					user: newUserId,
					referralEarnings: newUserEarnings,
				}).toUpperCase()}`,
			})
			await newUserEarning.save()
			const newUser = (await this.user.findById(newUserId)) as UserDocument
			newUser.balance += newUserEarnings
			await newUser.save()
		}
	}

	async getReferralEarningsLeaderboard(
		timeFrame: 'weekly' | 'monthly' | 'all',
	): Promise<GetReferralLeaderboardResponse[]> {
		// Define the date range based on the timeFrame
		const now = new Date()
		let startDate: Date

		// Set the start date based on the time frame
		if (timeFrame === 'weekly') {
			// Set to the beginning of the current week (Sunday)
			startDate = new Date(now)
			startDate.setDate(now.getDate() - now.getDay())
			startDate.setHours(0, 0, 0, 0)
		} else if (timeFrame === 'monthly') {
			// Set to the beginning of the current month
			startDate = new Date(now.getFullYear(), now.getMonth(), 1)
		} else {
			// 'all' or default: No date filtering for all-time
			startDate = new Date(0) // January 1, 1970
		}

		return this.earning.aggregate([
			{
				$match: {
					createdAt: { $gte: startDate },
					cashbackAmount: { $gt: 0 },
					earningsType: 'referral',
					referralUser: { $exists: true, $ne: null },
				},
			},
			{
				$group: {
					_id: '$user',
					totalReferralCommission: {
						$sum: '$cashbackAmount',
					},
					referralCount: { $sum: 1 },
				},
			},
			{
				// Sort by highest commission
				$sort: { totalReferralCommission: -1 },
			},
			{
				// Limit to top 7
				$limit: 7,
			},
			{
				// Lookup user details
				$lookup: {
					from: 'users',
					localField: '_id',
					foreignField: '_id',
					as: 'userDetails',
				},
			},
			{
				$unwind: '$userDetails',
			},
			{
				// Project final shape
				$project: {
					_id: 0,
					name: '$userDetails.name',
					totalReferralCommission: 1,
					referralCount: 1,
				},
			},
		])
	}
}
