import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { ReqUser, User, UserDocument } from 'shared/entities'
import {
	BankAccountDetails,
	BankAccountDetailsDocument,
} from '../../shared/entities/bank-account-details.entity'
import { UpdateBankAccountDto } from './dto/update-bank-account.dto'
import { GetBankAccountDataResponse } from './types'

@Injectable()
export class BankAccountDetailsService {
	constructor(
		@InjectModel(BankAccountDetails.name)
		private bankAccountDetails: Model<BankAccountDetailsDocument>,

		@InjectModel(User.name)
		private user: Model<UserDocument>,
	) {}

	async getBankAccountDetails(
		user: ReqUser,
	): Promise<GetBankAccountDataResponse> {
		const result = await this.bankAccountDetails
			.findOne({
				user: new Types.ObjectId(user.id),
			})
			.populate('user', 'address postcode')
			.lean()

		const bankAccountDetails: GetBankAccountDataResponse = {
			holderName: result?.holderName ?? '',
			accountNumber: result?.accountNumber ?? '',
			ifsc: result?.ifsc ?? '',
			upi: result?.upi ?? '',
			bankName: result?.bankName ?? '',
			branchName: result?.branchName ?? '',
			address: result?.user?.address ?? '',
			postcode: result?.user?.postcode ?? '',
		}

		return bankAccountDetails
	}

	async updateBankAccountDetails(body: UpdateBankAccountDto, email: string) {
		const user = (await this.user.findOne({ email: email })) as UserDocument
		const { address, postcode } = body

		// If bankAccountDetails for the user doesn't exist, it will be created due to the 'upsert: true' option
		await this.bankAccountDetails.findOneAndUpdate(
			{ user: user?._id },
			{ $set: body },
			{ new: true, upsert: true },
		)

		const result = await this.user.updateOne(
			{ _id: user._id },
			{ $set: { address: address, postcode: postcode.toString() } },
		)
		return true
	}

	async updateUpiId(upiId: string, email: string) {
		const user = (await this.user.findOne({ email: email })) as UserDocument

		let bankAccountDetails = await this.bankAccountDetails.findOne({
			user: user._id,
		})
		if (!bankAccountDetails) {
			bankAccountDetails = new this.bankAccountDetails({ user: user._id })
		}
		await this.bankAccountDetails.findOneAndUpdate(
			{ user: user._id },
			{
				$set: {
					upi: upiId,
				},
			},
			{ upsert: true }, // This option creates a new document if no documents match the filter
		)
		return true
	}
}
