import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import mongoose, { Model, Types } from 'mongoose'
import { ReqUser, User, UserDocument } from 'shared/entities'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { Status } from 'shared/types'
import { GetUserCashbackDto, GetUserHistoryDto } from './dto/user.dto'
import {
	GetCbHistoryResponse,
	GetUsersByReferralCodeResponse,
} from './types/user.types'
import { convertIstToUtc } from './user.helper'
import { UserService } from './user.service'

@Injectable()
export class UserEarningService {
	constructor(
		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>,
	) {
		// private userService: UserService,
	}

	// async getUserEarningsCount(
	// 	id: Types.ObjectId,
	// 	status: 'pending' | 'confirmed' | 'cancelled',
	// ) {
	// 	return await this.earning
	// 		.countDocuments({ user: new mongoose.Types.ObjectId(id), status })
	// 		.exec()
	// }

	async getUserEarningsCount(
		id: Types.ObjectId,
		status: 'pending' | 'confirmed' | 'cancelled',
		includeMigrated = true,
	) {
		const queryStatus =
			status === 'pending'
				? ['pending', 'tracked_for_confirm', 'tracked_for_cancel']
				: [status]

		const query: any = {
			user: new mongoose.Types.ObjectId(id),
			status: { $in: queryStatus },
		}

		if (!includeMigrated) {
			query.migrated = { $ne: true }
		}

		return await this.earning.countDocuments(query).exec()
	}

	async getCashBackHistory(
		queryParams: GetUserCashbackDto,
		loggedUser: UserDocument,
	): Promise<GetCbHistoryResponse> {
		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}
		console.log('🚀 ~ UserEarningService ~ loggedUser:', loggedUser)

		const page = queryParams.page ?? 1

		const skip = (page - 1) * queryParams.pageSize

		const aggregationPipeline = []

		// Match stage for user referral
		aggregationPipeline.push({
			$match: {
				user: loggedUser._id,
			},
		})

		// Lookup stage to populate "store" field
		aggregationPipeline.push({
			$lookup: {
				from: 'stores',
				localField: 'store',
				foreignField: '_id',
				as: 'store',
			},
		})

		// Unwind the "store" array to get individual documents
		aggregationPipeline.push({
			$unwind: {
				path: '$store',
				preserveNullAndEmptyArrays: true,
			},
		})

		// Match stage for stores
		if (queryParams.stores && queryParams.stores.length > 0) {
			aggregationPipeline.push({
				$match: {
					'store.uid': { $in: queryParams.stores },
				},
			})
		}

		// Match stage for joined date
		if (queryParams.startDate && queryParams.endDate) {
			const startDateUtc = convertIstToUtc(queryParams.startDate)
			const endDateUtc = convertIstToUtc(queryParams.endDate)

			aggregationPipeline.push({
				$match: {
					createdAt: {
						$gte: new Date(startDateUtc),
						$lte: new Date(endDateUtc),
					},
				},
			})
		}

		// Match stage for status
		if (queryParams?.status && queryParams?.status?.length > 0) {
			aggregationPipeline.push({
				$match: {
					status: { $in: queryParams.status },
				},
			})
		}

		// Match stage for referenceId search
		if (queryParams.searchParam) {
			aggregationPipeline.push({
				$match: {
					referenceId: {
						$regex: queryParams?.searchParam ?? '',
						$options: 'i',
					},
				},
			})
		}

		// Sort stage
		if (
			queryParams.sortType &&
			['newest', 'oldest'].includes(queryParams.sortType)
		) {
			aggregationPipeline.push({
				$sort: {
					createdAt: queryParams.sortType === 'newest' ? -1 : 1,
				},
			})
		}

		if (queryParams.sortType === 'cashbackAmount') {
			aggregationPipeline.push({
				$sort: {
					cashbackAmount: queryParams.sortType === 'cashbackAmount' ? -1 : 1,
				},
			})
		}

		// Add a facet stage to get both documents and total count
		aggregationPipeline.push({
			$facet: {
				// Stage to process documents
				documents: [
					{ $skip: skip },
					{ $limit: queryParams.pageSize },

					{
						// Project stage to shape the output documents
						$project: {
							_id: 0,
							orderAmount: '$saleAmount',
							cashbackAmount: '$cashbackAmount',
							// storeLogo: '$store.logo.secureUrl',
							storeLogo: {
								$ifNull: [
									'$store.logo.secureUrl',
									'https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg?20200913095930',
								], // Handle missing store logo
							},
							storeBgColor: {
								$ifNull: ['$store.bgColor', '#FFFFFF'], // Handle missing store logo
							},

							referenceId: '$referenceId',
							orderDate: '$createdAt',
							approxConfirmDate: '$confirmDate',
							remarks: '$remarks',
							status: {
								$switch: {
									branches: [
										{ case: { $eq: ['$status', 'pending'] }, then: 'pending' },
										{
											case: { $eq: ['$status', 'tracked_for_confirm'] },
											then: 'pending',
										},
										{
											case: { $eq: ['$status', 'tracked_for_cancel'] },
											then: 'pending',
										},
										{
											case: { $eq: ['$status', 'confirmed'] },
											then: 'confirmed',
										},
										{
											case: { $eq: ['$status', 'cancelled'] },
											then: 'cancelled',
										},
									],
									default: 'pending',
								},
							},
						},
					},
				],
				totalCount: [
					{ $count: 'total' }, // Count total documents
				],
			},
		})

		const userCashback = await this.earning
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			.aggregate(aggregationPipeline as any)
			.exec()

		return {
			cbItems: userCashback[0].documents,
			pagination: {
				page: queryParams.page,
				pageSize: userCashback[0].documents?.length,
				total: userCashback[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}
}
