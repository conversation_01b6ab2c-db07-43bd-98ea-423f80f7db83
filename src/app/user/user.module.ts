import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { PersonalInterestModule } from 'app/context/personal-interest/personal-interest.module'
import {
	BankAccountDetails,
	BankAccountDetailsSchema,
	Click,
	ClickSchema,
	CookieSession,
	CookieSessionSchema,
} from 'shared/entities'
import { Earning, EarningSchema } from 'shared/entities/earning.entity'
import { Otp, OtpSchema } from '../../shared/entities/otp.entity'
import { User, UserSchema } from '../../shared/entities/user.entity'
import { BankAccountDetailsService } from './bank-details.service'
import { ReferralCampaignController } from './referral-campaign.controller'
import { ReferralCommissionService } from './referral-commission.service'
import { UpdateDetailsOtpStrategy } from './update-details-otp-strategy'
import { UserEarningService } from './user-earning.service'
import { UserController } from './user.controller'
import { UserService } from './user.service'

@Module({
	imports: [
		PersonalInterestModule,
		MongooseModule.forFeature([
			{
				name: User.name,
				schema: UserSchema,
			},
			{
				name: BankAccountDetails.name,
				schema: BankAccountDetailsSchema,
			},
			{
				name: Earning.name,
				schema: EarningSchema,
			},
			{
				name: Otp.name,
				schema: OtpSchema,
			},
			{
				name: Earning.name,
				schema: EarningSchema,
			},
			{
				name: Click.name,
				schema: ClickSchema,
			},
			{
				name: CookieSession.name,
				schema: CookieSessionSchema,
			},
		]),
	],

	controllers: [UserController, ReferralCampaignController],
	providers: [
		UserService,
		BankAccountDetailsService,
		UpdateDetailsOtpStrategy,
		UserEarningService,
		ReferralCommissionService,
	],
	exports: [
		UserService,
		BankAccountDetailsService,
		UserEarningService,
		ReferralCommissionService,
	],
})
export class UserModule {}
