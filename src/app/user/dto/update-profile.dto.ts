import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import destr from 'destr'
import { Types } from 'mongoose'

export class UpdateProfileDto {
	@ApiProperty({
		example: '<PERSON> Doe',
		description: 'Name of the user',
		required: true,
	})
	@IsString()
	@IsOptional()
	name?: string

	@ApiProperty({
		example: '6f9d88f7d6f3e3b3f8f3f8f3',
		description: 'Select multiple categories you are interested from this list',
		required: false,
	})
	@IsString()
	@IsOptional()
	personalInterest?: Types.ObjectId

	@ApiProperty({
		example: true || false,
		description: 'select true or false',
		required: false,
	})
	@IsBoolean()
	@IsOptional()
	sendNotification?: boolean
}

export class UpdateProfileWithImageDto {
	@ApiProperty({ type: 'string', format: 'binary', required: false })
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	profileImage?: any

	@ApiProperty({ type: UpdateProfileDto, required: false })
	@Transform(data => (data ? destr(data.value) : {}))
	profileData: UpdateProfileDto
}
