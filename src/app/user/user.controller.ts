import {
	Body,
	Controller,
	Get,
	Patch,
	Post,
	Query,
	UploadedFile,
	UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiConsumes, ApiResponse, ApiTags } from '@nestjs/swagger'
import { Auth, ClientInfo, ClientInfoData, FileUpload } from 'shared/decorators'
import { User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { BankAccountDetailsService } from './bank-details.service'
import {
	OtpDto,
	UpdateBankAccountDto,
	UpdateCredentialsDto,
	UpdateProfileWithImageDto,
	UpdateUpiDto,
} from './dto'
import { UserOverviewResponse } from './dto/overview'
import { UserListResponseDto } from './dto/user-list.dto'
import { GetUserCashbackDto, GetUserHistoryDto } from './dto/user.dto'
import { GetBankAccountDataResponse, GetUserProfileResponseItem } from './types'
import {
	GetCbHistoryResponse,
	GetUsersByReferralCodeResponse,
} from './types/user.types'
import { UpdateDetailsOtpGuard } from './update-details-otp-auth.guard'
import { UserEarningService } from './user-earning.service'
import { UserService } from './user.service'

@ApiTags('Users')
@Controller('users')
export class UserController {
	constructor(
		private readonly userService: UserService,
		private readonly userEarningService: UserEarningService,
		private readonly bankAccountDetails: BankAccountDetailsService,
	) {}

	@ApiResponse({
		type: GetBankAccountDataResponse,
	})
	@Auth()
	@Get('get-bank-details')
	async getBankDetails(
		@User() user: ReqUser,
	): Promise<GetBankAccountDataResponse> {
		return await this.bankAccountDetails.getBankAccountDetails(user)
	}

	@ApiResponse({
		type: Boolean,
	})
	@Auth()
	@Post('update-bank-details')
	async updateBankDetails(
		@User() user: ReqUser,
		@Body() bankDetails: UpdateBankAccountDto,
	) {
		return await this.bankAccountDetails.updateBankAccountDetails(
			bankDetails,
			user.email,
		)
	}

	@ApiResponse({
		type: Boolean,
	})
	@Auth()
	@Patch('update-upi')
	async updateUpiId(@User() user: ReqUser, @Body() updateUpiDto: UpdateUpiDto) {
		return this.bankAccountDetails.updateUpiId(updateUpiDto.upi, user.email)
	}

	@ApiResponse({
		type: GetUserProfileResponseItem,
	})
	@Auth()
	@Get('me')
	async getProfileDetails(
		@User() user: ReqUser,
	): Promise<GetUserProfileResponseItem> {
		return this.userService.getUserProfileById(user.id)
	}

	@Auth()
	@ApiResponse({ type: UserOverviewResponse })
	@Get('overview')
	async getUsersOverViewDetails(@User() user: ReqUser) {
		return this.userService.getUsersOverViewDetails(user)
	}

	@Auth()
	@ApiResponse({ type: GetUsersByReferralCodeResponse })
	@Get('referral-history')
	async getUsersByReferralCode(
		@User() user: ReqUser,
		@Query() queryParams: GetUserHistoryDto,
	): Promise<GetUsersByReferralCodeResponse> {
		return this.userService.getReferralHistory(user, queryParams)
	}

	@Auth()
	@ApiResponse({ type: GetCbHistoryResponse })
	@Get('cashback-history')
	async getCashbackHistory(
		@User() user: ReqUser,
		@Query() queryParams: GetUserCashbackDto,
	): Promise<GetCbHistoryResponse> {
		return this.userService.getCashBackHistory(user, queryParams)
	}

	@Auth()
	@Post('update-profile')
	@ApiResponse({
		type: Boolean,
	})
	@ApiConsumes('multipart/form-data')
	@ApiBody({ type: UpdateProfileWithImageDto })
	@FileUpload('profileImage')
	async updateProfile(
		@User() user: ReqUser,
		@UploadedFile() file: Express.Multer.File,
		@Body() payloadDto: UpdateProfileWithImageDto,
	) {
		return this.userService.updateProfile(
			payloadDto.profileData,
			user.id,
			file?.buffer,
		)
	}

	@Auth()
	@Post('update-credentials')
	async sendOtpToUpdateUserCredentials(
		@User() user: ReqUser,
		@Body() updateCredentials: UpdateCredentialsDto,
	) {
		return await this.userService.sendOtpToUpdateUserCredentials(
			user.email,
			updateCredentials,
		)
	}

	@Auth()
	@UseGuards(UpdateDetailsOtpGuard)
	@Post('verify-credentials-otp')
	async verifyOtpToUpdateCredentials(
		@User() user: ReqUser,
		@Body() otpDto: OtpDto,
		@ClientInfo() clientInfo: ClientInfoData,
	) {
		return true
	}

	@Get('get-personal-interest')
	async getAllPersonalInterestData() {
		return await this.userService.getAllPersonalInterestData()
	}

	@Post('update-referral-code')
	async updateAllUsersReferralCode() {
		return this.userService.updateAllUsersReferralCode()
	}

	@ApiResponse({ type: UserListResponseDto })
	@Get('list')
	async listUsers() {
		return this.userService.listUsers()
	}
}
