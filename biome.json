{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "files": {"ignore": ["dist", "node_modules", "json-dump"], "ignoreUnknown": true}, "formatter": {"enabled": true, "ignore": ["dist", "node_modules", "json-dump"]}, "javascript": {"formatter": {"arrowParentheses": "asNeeded", "enabled": true, "indentStyle": "tab", "quoteProperties": "asNeeded", "quoteStyle": "single", "semicolons": "asNeeded"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}, "linter": {"enabled": true, "ignore": ["dist", "node_modules", "global.d.ts", "app/migration/old-types/*.ts", "json-dump"], "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn", "noThenProperty": "off"}, "style": {"all": true, "noDefaultExport": "off", "noParameterProperties": "off", "noUselessElse": "error", "useAsConstAssertion": "warn", "useImportType": "off", "useConst": "error", "useNamingConvention": "warn", "useShorthandAssign": "warn", "useWhile": "warn"}}}, "organizeImports": {"enabled": true, "ignore": ["dist", "node_modules", "json-dump"]}}