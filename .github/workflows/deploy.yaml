name: Build and Deploy to DigitalOcean Kubernetes

on:
  push:
    branches: [ not-right-now ]

env:
  REGISTRY: registry.digitalocean.com
  REGISTRY_NAME: icb-images
  IMAGE_NAME: icbbackend

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@main

    - name: Build container image
      run: docker build -t $(echo $REGISTRY)/$(echo $REGISTRY_NAME)/$(echo $IMAGE_NAME):$(echo $GITHUB_SHA | head -c7) .

    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
    
    - name: Build container image
      run: docker build -t $(echo $REGISTRY)/$(echo $REGISTRY_NAME)/$(echo $IMAGE_NAME):$(echo $GITHUB_SHA | head -c7) .

    - name: Log in to DigitalOcean Container Registry
      run: doctl registry login --expiry-seconds 1200

    - name: Push image to DigitalOcean Container Registry
      run: docker push $(echo $REGISTRY)/$(echo $REGISTRY_NAME)/$(echo $IMAGE_NAME):$(echo $GITHUB_SHA | head -c7)

    - name: Update deployment file
      run: |
          sed -i 's|image: .*|image: '$(echo $REGISTRY)/$(echo $REGISTRY_NAME)/$(echo $IMAGE_NAME):$(echo $GITHUB_SHA | head -c7)'|' $GITHUB_WORKSPACE/kubernetes/deployment.yaml
   
    - name: Save DigitalOcean kubeconfig
      run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ secrets.CLUSTER_NAME }}
    
    - name: Create Kubernetes Secrets
      run: |
        kubectl create secret generic icbbackend-secrets \
          --from-literal=ADMIN_ALTERNATE_EMAIL="${{ secrets.ADMIN_ALTERNATE_EMAIL }}" \
          --from-literal=APP_PORT="${{ secrets.APP_PORT }}" \
          --from-literal=AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}" \
          --from-literal=AWS_BUCKET_NAME="${{ secrets.AWS_BUCKET_NAME }}" \
          --from-literal=AWS_REGION="${{ secrets.AWS_REGION }}" \
          --from-literal=AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}" \
          --from-literal=CLOUDINARY_API_KEY="${{ secrets.CLOUDINARY_API_KEY }}" \
          --from-literal=CLOUDINARY_API_SECRET="${{ secrets.CLOUDINARY_API_SECRET }}" \
          --from-literal=CLOUDINARY_NAME="${{ secrets.CLOUDINARY_NAME }}" \
          --from-literal=CORES_ENABLED="${{ secrets.CORES_ENABLED }}" \
          --from-literal=DB_HOST_URL="${{ secrets.DB_HOST_URL }}" \
          --from-literal=DB_NAME="${{ secrets.DB_NAME }}" \
          --from-literal=HASH_SECRET="${{ secrets.HASH_SECRET }}" \
          --from-literal=JWT_SECRET="${{ secrets.JWT_SECRET }}" \
          --from-literal=MAILGUN_API_KEY="${{ secrets.MAILGUN_API_KEY }}" \
          --from-literal=MAILGUN_DOMAIN="${{ secrets.MAILGUN_DOMAIN }}" \
          --from-literal=MAILGUN_FROM="${{ secrets.MAILGUN_FROM }}" \
          --from-literal=MEILISEARCH_KEY="${{ secrets.MEILISEARCH_KEY }}" \
          --from-literal=MEILISEARCH_NAME="${{ secrets.MEILISEARCH_NAME }}" \
          --from-literal=MEILISEARCH_URL="${{ secrets.MEILISEARCH_URL }}" \
          --from-literal=NODE_ENV="${{ secrets.NODE_ENV }}" \
          --from-literal=ORIGIN="${{ secrets.ORIGIN }}" \
          --from-literal=RAZORPAY_KEY_ID="${{ secrets.RAZORPAY_KEY_ID }}" \
          --from-literal=RAZORPAY_KEY_SECRET="${{ secrets.RAZORPAY_KEY_SECRET }}" \
          --from-literal=REDIS_HOST="${{ secrets.REDIS_HOST }}" \
          --from-literal=REDIS_PASSWORD="${{ secrets.REDIS_PASSWORD }}" \
          --from-literal=REDIS_PORT="${{ secrets.REDIS_PORT }}" \
          --from-literal=REDIS_URL="${{ secrets.REDIS_URL }}" \
          --from-literal=SESSION_SECRET="${{ secrets.SESSION_SECRET }}" \
          --from-literal=SMS_AUTH_KEY="${{ secrets.SMS_AUTH_KEY }}" \
          --from-literal=SMS_COUNTRY="${{ secrets.SMS_COUNTRY }}" \
          --from-literal=SMS_ROUTE="${{ secrets.SMS_ROUTE }}" \
          --from-literal=SMS_SENDER="${{ secrets.SMS_SENDER }}" \
          --from-literal=SWAGGER_STATS_PASSWORD="${{ secrets.SWAGGER_STATS_PASSWORD }}" \
          --from-literal=SWAGGER_STATS_USERNAME="${{ secrets.SWAGGER_STATS_USERNAME }}" \
          --from-literal=SOURCE_URL="${{ secrets.SOURCE_URL }}" \
          --from-literal=TARGET_URL="${{ secrets.TARGET_URL }}" \
          --from-literal=SOURCE_DATABASE="${{ secrets.SOURCE_DATABASE }}" \
          --from-literal=TARGET_DATABASE="${{ secrets.TARGET_DATABASE }}" \
          --dry-run=client -o yaml | kubectl apply -f -

    - name: Verify Secret Creation
      run: |
        if kubectl get secret icbbackend-secrets &> /dev/null; then
          echo "Secret 'icbbackend-secrets' exists"
          else
            echo "Secret 'icbbackend-secrets' does not exist"
            exit 1
          fi

    - name: Deploy to DigitalOcean Kubernetes
      run: |
        kubectl apply -f $GITHUB_WORKSPACE/kubernetes/configmap.yaml
        kubectl apply -f $GITHUB_WORKSPACE/kubernetes/deployment.yaml
        kubectl apply -f $GITHUB_WORKSPACE/kubernetes/service.yaml
        kubectl apply -f $GITHUB_WORKSPACE/kubernetes/ingress.yaml
        kubectl apply -f $GITHUB_WORKSPACE/kubernetes/hpa.yaml

    - name: Verify deployment
      run: |
        kubectl rollout status deployment/icbbackend
        kubectl get hpa icbbackend-hpa
      
    - name: Cleanup old images
      run: |
        # List all tags, sort them in reverse order (newest first), and get all but the first two
          IMAGES_TO_DELETE=$(doctl registry repository list-tags $IMAGE_NAME --format Tag --no-header | sort -r | tail -n +3)
          
          # Delete old images
          for TAG in $IMAGES_TO_DELETE
          do
            echo "Deleting image with tag: $TAG"
            doctl registry repository delete-tag $IMAGE_NAME $TAG --force
          done
          
        # Run garbage collection
        echo "Running garbage collection"
        doctl registry garbage-collection start --include-untagged-manifests -f
  
    - name: Verify image cleanup
      run: |
        echo "Remaining images in the repository:"
        doctl registry repository list-tags $IMAGE_NAME --format Tag --no-header  
