apiVersion: apps/v1
kind: Deployment
metadata:
  name: icbbackend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: icbbackend
  template:
    metadata:
      labels:
        app: icbbackend
    spec:
      containers:
        - name: icbbackend
          image: registry.digitalocean.com/icb-images/icbbackend:latest
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: icbbackend-config
            - secretRef:
                name: icbbackend-secrets
          resources:
            requests:
              cpu: 100m # Request 0.1 CPU cores
              memory: 128Mi
