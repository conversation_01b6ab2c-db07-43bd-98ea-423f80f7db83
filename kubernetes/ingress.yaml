apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: icbbackend-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  tls:
    - hosts:
        - api-main.indiancashback.com
      secretName: icbbackend-tls
  rules:
    - host: api-main.indiancashback.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: icbbackend-service
                port:
                  number: 80
