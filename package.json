{"name": "icb-backend", "version": "0.0.1", "private": true, "license": "LICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev2": "nest start -b swc --type-check -w", "dev": "nest start -w", "start:debug": "nest start --debug --watch", "start:prod": "bun run dist/main", "lint": "bunx @biomejs/biome check --write .", "postinstall": "[ -z \"$CI\" ] && [ -d .git ] && bunx lefthook install || echo 'Skipping lefthook installation'"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@faker-js/faker": "^8.4.1", "@formkit/tempo": "^0.0.15", "@getbrevo/brevo": "^2.2.0", "@grammyjs/runner": "^2.0.3", "@nest-toolbox/http-logger-middleware": "^1.5.1", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^5.2.0", "@scalar/nestjs-api-reference": "^0.3.190", "@types/jsonwebtoken": "^9.0.9", "camelcase-keys": "^9.1.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.6.0", "colorette": "^2.0.20", "compression": "^1.8.0", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "defu": "^6.1.4", "destr": "^2.0.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-session": "^1.18.1", "form-data": "^4.0.2", "geoip-lite": "^1.4.10", "grammy": "^1.36.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "magic-regexp": "^0.8.0", "mailgun.js": "^10.4.0", "meilisearch": "^0.38.0", "module-alias": "^2.2.3", "moment-timezone": "^0.5.48", "mongodb": "^6.15.0", "mongoose": "^8.13.2", "multer": "1.4.5-lts.1", "ncsrf": "^1.1.0", "nest-problem-details-filter": "^0.1.1", "nestjs-razorpay": "^2.0.1", "ofetch": "^1.4.1", "ohash": "^1.1.6", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-google-oauth20": "^2.0.0", "radash": "^12.1.0", "razorpay": "^2.9.6", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "swagger-stats": "^0.99.7", "useragent": "^2.3.0", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@swc/cli": "^0.3.14", "@swc/core": "^1.11.20", "@types/compression": "^1.7.5", "@types/connect-redis": "^0.0.23", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/geoip-lite": "^1.4.4", "@types/module-alias": "^2.0.4", "@types/multer": "^1.4.12", "@types/node": "^20.17.30", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/swagger-stats": "^0.95.11", "@types/useragent": "^2.3.4", "cspell": "^8.18.1", "lefthook": "^1.11.8", "mkcert": "^3.2.0", "source-map-support": "^0.5.21", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}